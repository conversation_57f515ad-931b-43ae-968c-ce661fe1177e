/*
 * @Author: ling
 * @Date: 2025-05-28 16:29:34
 * @LastEditors: ling
 * @LastEditTime: 2025-05-29 09:26:32
 * @Description:
 * Copyright © Dongguan Xiaotun Intelligent Technology Co., Ltd. All Rights Reserved
 * Address: Room 1201, Building G4, Songshan Lake International Entrepreneurship and Innovation Community, Dongguan City
 */
#ifndef XTAISMAP_H
#define XTAISMAP_H

#include <cstring>
#include <vector>

namespace xtai_simulation
{
    class XTAISMap
    {
    public:
        XTAISMap();
        ~XTAISMap();
        std::vector<std::vector<unsigned char>> GetFullAisMap();

        int GetPartOfAisMap(unsigned int map_size, unsigned int start_x, unsigned int start_y, std::vector<std::vector<unsigned char>> &part_map);

    private:
        std::vector<std::vector<unsigned char>> aisMap;
    };
} // xtai_simulation

#endif