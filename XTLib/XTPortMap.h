/*
 * @Author: ling
 * @Date: 2025-05-28 16:29:34
 * @LastEditors: lingeros
 * @LastEditTime: 2025-05-30 17:31:52
 * @Description:
 * Copyright © Dongguan Xiaotun Intelligent Technology Co., Ltd. All Rights Reserved
 * Address: Room 1201, Building G4, Songshan Lake International Entrepreneurship and Innovation Community, Dongguan City
 */
#ifndef __XTPORTMAP_H__
#define __XTPORTMAP_H__

#include <cstring>
#include <vector>

namespace xtai_simulation
{
    class XTPortMap
    {
    public:
        XTPortMap();
        ~XTPortMap();
        std::vector<std::vector<unsigned char>> GetFullPortMap();
        int GetPartOfPortMap(unsigned int map_size, unsigned int start_x, unsigned int start_y, std::vector<std::vector<unsigned char>> &part_map);       
        std::vector<std::vector<unsigned int>> GetDockList();
    private:
        int row = 15840;
        int col = 12000;
        std::vector<std::vector<unsigned int>> dockList;
        std::vector<std::vector<unsigned char>> portMap;
    };
}
#endif
