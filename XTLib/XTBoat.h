/*
 * @Author: ling
 * @Date: 2025-05-28 16:29:34
 * @LastEditors: ling
 * @LastEditTime: 2025-05-29 09:26:32
 * @Description:
 * Copyright © Dongguan Xiaotun Intelligent Technology Co., Ltd. All Rights Reserved
 * Address: Room 1201, Building G4, Songshan Lake International Entrepreneurship and Innovation Community, Dongguan City
 */
#ifndef XTBOAT_H
#define XTBOAT_H
#include <string.h>
#include <vector>
#include <random>
#define M_PI 3.1415926535
namespace xtai_simulation
{

    class XTBoat
    {
    public:
        XTBoat();
        ~XTBoat() = default;
        double baseLat;
        double baseLon;
        float deltat;
        std::vector<double> preState; // utc,roll,pitch,yaw,rolls,pitchs,yaws,lat,lon,alt,vx,vy,vz,xac,yzc,zac
        /*
        typedef struct{
        uint8_t kind;
        uint8_t num;
        float  roll;
        float  pitch;
        float  yaw;
        float  rollspeed;
        float  pitchspeed;
        float  yawspeed;
        double  lat;
        double  lon;
        double  alt;
        float vx;
        float vy;
        float vz;
        float xacc;
         float yacc;
        float zacc;
        uint8_t  reserve[6];
        } kinematic_state;
        */
        std::mt19937 gen;
	    std::uniform_real_distribution<> dis;

        void DynamicCalculate(std::vector<float> input);

        std::vector<double> StateGet() const;

        static float GetSystemUTC();

        static void PosToPos(double pos[2], double target[2], float lengthAngle[2]);
    };

} // xtai_simulation

#endif // XTBOAT_H
