#ifndef XTBOATMODEL2_H
#define XTBOATMODEL2_H
#include "XTBoat.h"
#include "XTBoatPID.h"
#include <cstring>
#include <termios.h>

namespace xtai_simulation
{
	class XTBoatModel2:public XTBoat
	{
	public:
		XTBoatModel2()=default;
		XTBoatModel2(double baselat, double baselon, float prephi);
		void SetEnviro(double mp, double v_c, double beta_c);
		void DynamicCalculate(const std::vector<float> input);
		std::vector<double> StateGet();
		double n[2];                //propeller revolutions (rps), n = [ n_left n_right ]' 
	private:
		XTBoatPID headingcontroller;
		XTBoatPID velocitycontroller;
		double x[12];               //initial values for x = [ u v w p q r x y z phi theta psi ]'
		double xdot[12];
		double h;                   //sampling time [s]
		double U;
		double outx[12];

		void Dynamic(const double x[10], double ui[2], double xdot[10],
							double *U);
		double lat2m = 0;         
		double lng2m = 0;  
	};
}

#endif
