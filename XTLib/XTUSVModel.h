/*
 * @Author: ling
 * @Date: 2025-05-28 16:29:34
 * @LastEditors: lingeros
 * @LastEditTime: 2025-05-30 17:34:49
 * @Description: XTAI Simulation Lib
 * @Copyright:  Copyright © Dongguan Xiaotun Intelligent Technology Co., Ltd. All Rights Reserved
 * @Address:    Room 1201, Building G4, Songshan Lake International Entrepreneurship and Innovation Community, Dongguan City
 * @Phone:      0769-89887358
 * @Website:    www.xiaotunai.com
 */
#ifndef XTUSVMODEL_H
#define XTUSVMODEL_H
#include "XTUSV.h"
#include <vector>
#include <cstring>

namespace xtai_simulation
{
    class XTUSVModel
    {
    public:
        /*
         * @description: 构造函数
         * @param randNum: 随机数
         * @param baseLat: 基准:纬度/x pos
         * @param baseLon: 基准:经度/y pos
         * @param prePhi: 偏航角
         * @return: 无
         */
        XTUSVModel(int randNum, int type, double baseLat, double baseLon, float prePhi);

        ~XTUSVModel();

        /*
         * @description: 获取USV状态
         * @return: USV状态
         */
        std::vector<double> GetUSVState();

        /*
         * @description: 获取USV控制结果
         * @return: USV控制结果
         */
        std::vector<double> GetUSVActuatorState();

        /*
         * @description: 获取全局AIS地图
         */
        std::vector<std::vector<unsigned char>> GetFullAisMap();
        /*
         * @description: 获取局部AIS地图
         * @param map_size: 地图大小
         * @param start_x: 起始x坐标
         * @param start_y: 起始y坐标
         * @param part_map: 传入一个vector用来接收局部地图
         */
        int GetPartOfAisMap(unsigned int map_size, unsigned int start_x, unsigned int start_y, std::vector<std::vector<unsigned char>> &part_map);

        /*
         * @description: 获取局部AIS地图
         * @param map_size: 地图大小
         * @param start_x: 起始x坐标
         * @param start_y: 起始y坐标
         * @param part_map: 传入一个vector用来接收局部地图
         */
        int GetNeighbor(unsigned int map_size, unsigned int start_x, unsigned int start_y, std::vector<std::vector<unsigned char>> &part_map);

        /*
         * @description: 获取全局地图
         */
        std::vector<std::vector<unsigned char>> GetFullLocalMap();

        /*
         * @description: 获取局部地图
         * @param map_size: 地图大小，例如需要200*200的地图，那么map_size=200
         * @param start_x: 起始x坐标
         * @param start_y: 起始y坐标
         * @param part_map: 传入一个vector用来接收局部地图
         */
        int GetPartOfLocalMap(unsigned int map_size, unsigned int start_x, unsigned int start_y, std::vector<std::vector<unsigned char>> &part_map);

        /*
         * @description: USV控制
         * @param input: 控制输入
         * @return: 控制结果
         */
        std::vector<double> USVControl(std::vector<float> input);

        /*
         * @description: 获取当前USV是否碰到障碍物
         * @return: 是否碰到
         *
         */
        bool GetObsFlag();

        /*
         * @description: 获取码头列表
         */
        std::vector<std::vector<unsigned int>> GetDockList();

        /*
         * @description: 获取港口地图
         */
        std::vector<std::vector<unsigned char>> GetFullPortMap();

        /*
         * @description: 获取局部港口地图
         * @param map_size: 地图大小，例如需要200*200的地图，那么map_size=200
         * @param start_x: 起始x坐标
         * @param start_y: 起始y坐标
         * @param part_map: 传入一个vector用来接收局部地图
         */
        int GetPartOfPortMap(unsigned int map_size, unsigned int start_x, unsigned int start_y, std::vector<std::vector<unsigned char>> &part_map);

    private:
        XTUSV *xtusv;
        XTUSV *GetXTUSV();
    };
} // xtai_simulation

#endif // XTUSVMODEL_H