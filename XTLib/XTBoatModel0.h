#ifndef XTBOATMODEL0_H
#define XTBOATMODEL0_H
#include "XTBoat.h"
#include "XTBoatPID.h"
#include <cstring>
#include <termios.h>

namespace xtai_simulation
{
	class XTBoatModel0:public XTBoat
	{
	public:
		XTBoatModel0()=default;
		XTBoatModel0(double baselat, double baselon, float prephi);
		void DynamicCalculate(const std::vector<float> input);
		std::vector<double> StateGet();
		double n[2];                //propeller revolutions (rps), n = [ n_left n_right ]' 
	private:
		XTBoatPID headingcontroller;
		XTBoatPID velocitycontroller;
		double x[12];               //initial values for x = [ u v w p q r x y z phi theta psi ]'
		double h;                   //sampling time [s]
		double U;
		double outx[12]; 
		void Dynamic(double x[12], const double n[2], double *U);
		double lat2m = 0;         
		double lng2m = 0;  
		double xdot[12];
	};
}

#endif
