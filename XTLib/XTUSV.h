/*
 * @Author: ling
 * @Date: 2025-05-28 16:29:34
 * @LastEditors: lingeros
 * @LastEditTime: 2025-06-12 17:42:25
 * @Description:
 * Copyright © Dongguan Xiaotun Intelligent Technology Co., Ltd. All Rights Reserved
 * Address: Room 1201, Building G4, Songshan Lake International Entrepreneurship and Innovation Community, Dongguan City
 */
#ifndef XTUSV_H
#define XTUSV_H

#include <cstring>
#include <vector>

#include "XTBoatModel0.h"
#include "XTBoatModel1.h"
#include "XTBoatModel2.h"
#include "XTAISMap.h"
#include "XTLocalMap.h"
#include "XTPortMap.h"

namespace xtai_simulation
{
    class XTUSV
    {
    public:
        XTUSV() = default;
        XTUSV(int randNum, int preType, double baseLat, double baseLon, float prePhi);
        int type;

        void DynamicCalculate(std::vector<float> input);

        std::vector<double> StateGet();
        std::vector<double> ActuatorStateGet();
        std::vector<std::vector<unsigned char>> GetFullAisMap();
        int GetPartOfAisMap(unsigned int map_size, unsigned int start_x, unsigned int start_y, std::vector<std::vector<unsigned char>> &part_map);
        std::vector<std::vector<unsigned char>> GetFullLocalMap();
        int GetPartOfLocalMap(unsigned int map_size, unsigned int start_x, unsigned int start_y, std::vector<std::vector<unsigned char>> &part_map);
        bool GetObsFlag();
        /*
         * @description: 获取码头列表
         */
        std::vector<std::vector<unsigned int>> GetDockList();

        /*
         * @description: 获取港口地图
         */
        std::vector<std::vector<unsigned char>> GetFullPortMap();

        /*
         * @description: 获取局部港口地图
         * @param map_size: 地图大小，例如需要200*200的地图，那么map_size=200
         * @param start_x: 起始x坐标
         * @param start_y: 起始y坐标
         * @param part_map: 传入一个vector用来接收局部地图
         */
        int GetPartOfPortMap(unsigned int map_size, unsigned int start_x, unsigned int start_y, std::vector<std::vector<unsigned char>> &part_map);

    private:
        double _baseLat;
        double _baseLon;
        XTBoatModel0 xtBoatModel0;
        XTBoatModel1 xtBoatModel1;
        XTBoatModel2 xtBoatModel2;
        std::vector<double> actuatorState;
        // XTAISMap xtAisMap;
        // XTLocalMap xtLocalMap;
        // XTPortMap xtPortMap;
    };
} // xtai_simulation

#endif // XTUSV_H
