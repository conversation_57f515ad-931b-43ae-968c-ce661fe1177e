/*
 * @Author: ling
 * @Date: 2025-05-28 16:29:34
 * @LastEditors: ling
 * @LastEditTime: 2025-05-29 09:26:32
 * @Description:
 * Copyright © Dongguan Xiaotun Intelligent Technology Co., Ltd. All Rights Reserved
 * Address: Room 1201, Building G4, Songshan Lake International Entrepreneurship and Innovation Community, Dongguan City
 */
#ifndef XTBOATPID_H
#define XTBOATPID_H

#include <string.h>
#include <termios.h>
namespace xtai_simulation
{

    class XTBoatPID
    {
    public:
        XTBoatPID(float p, float i, float d, float maxSum, float minOutput, float maxOutput);
        XTBoatPID() = default;
        ~XTBoatPID() = default;

        float maxOutput;
        float minOutput;
        float maxSum;

        float err;
        float olderr;
        float sumerr;
        float kp, ki, kd;
        float PIDControl(float error);
    };

} // xtai_simulation

#endif // XTBOATPID_H
