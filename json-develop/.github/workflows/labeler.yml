name: "Pull Request Labeler"

on:
  pull_request_target:
    types: [opened, synchronize]

permissions:
  contents: read

jobs:
  label:
    permissions:
      contents: read
      pull-requests: write

    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - uses: srvaroa/labeler@e216fb40e2e6d3b17d90fb1d950f98bee92f65ce # master
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
