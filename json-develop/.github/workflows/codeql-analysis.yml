name: "Code scanning - action"

on:
  push:
    branches:
      - develop
      - master
      - release/*
  pull_request:
  schedule:
    - cron: '0 19 * * 1'
  workflow_dispatch:
  
concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  CodeQL-Build:

    runs-on: ubuntu-latest
    permissions:
      security-events: write

    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
      with:
        egress-policy: audit

    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@60168efe1c415ce0f5521ea06d5c2062adbeed1b # v3.28.17
      with:
        languages: c-cpp

    # Autobuild attempts to build any compiled languages  (C/C++, C#, or Java).
    # If this step fails, then you should remove it and run the build manually (see below)
    - name: Autobuild
      uses: github/codeql-action/autobuild@60168efe1c415ce0f5521ea06d5c2062adbeed1b # v3.28.17

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@60168efe1c415ce0f5521ea06d5c2062adbeed1b # v3.28.17
