name: Ubuntu

on:
  push:
    branches:
      - develop
      - master
      - release/*
  pull_request:
  workflow_dispatch:

permissions:
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

jobs:
  ci_test_gcc:
    runs-on: ubuntu-latest
    container: gcc:latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_gcc

  ci_infer:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.4.0
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_infer

  ci_static_analysis_ubuntu:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        target: [ci_test_amalgamation, ci_test_single_header, ci_cppcheck, ci_cpplint, ci_reproducible_tests, ci_non_git_tests, ci_offline_testdata, ci_reuse_compliance, ci_test_valgrind]
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: Install Valgrind
        run: sudo apt-get update ; sudo apt-get install -y valgrind
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ${{ matrix.target }}

  ci_static_analysis_clang:
    runs-on: ubuntu-latest
    container: silkeh/clang:dev
    strategy:
      matrix:
        target: [ci_test_clang, ci_clang_tidy, ci_test_clang_sanitizer, ci_clang_analyze, ci_single_binaries]
    steps:
      - name: Install git, clang-tools, iwyu (ci_single_binaries), and unzip
        run: apt-get update ; apt-get install -y git clang-tools iwyu unzip
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ${{ matrix.target }}

  ci_cmake_options:
    runs-on: ubuntu-latest
    container: ubuntu:focal
    strategy:
      matrix:
        target: [ci_cmake_flags, ci_test_diagnostics, ci_test_diagnostic_positions, ci_test_noexceptions, ci_test_noimplicitconversions, ci_test_legacycomparison, ci_test_noglobaludls]
    steps:
      - name: Install build-essential
        run: apt-get update ; apt-get install -y build-essential unzip wget git libssl-dev
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ${{ matrix.target }}

  ci_test_coverage:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Install dependencies and de_DE locale
        run: |
          sudo apt-get clean
          sudo apt-get update
          sudo apt-get install -y build-essential cmake lcov ninja-build make locales gcc-multilib g++-multilib
          sudo locale-gen de_DE
          sudo update-locale
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_coverage
      - name: Archive coverage report
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: code-coverage-report
          path: ${{ github.workspace }}/build/html
      - name: Publish report to Coveralls
        uses: coverallsapp/github-action@648a8eb78e6d50909eff900e4ec85cab4524a45b # v2.3.6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          path-to-lcov: ${{ github.workspace }}/build/json.info.filtered.noexcept

  ci_test_compilers_gcc_old:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        compiler: ['4.8', '4.9', '5', '6']
    container: ghcr.io/nlohmann/json-ci:v2.4.0
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Run CMake
        run: CXX=g++-${{ matrix.compiler }} cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_compiler_g++-${{ matrix.compiler }}

  ci_test_compilers_gcc:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        # older GCC docker images (4, 5, 6) fail to check out code
        compiler: ['7', '8', '9', '10', '11', '12', '13', '14', '15', 'latest']
    container: gcc:${{ matrix.compiler }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_compiler_default

  ci_test_compilers_clang:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        compiler: ['3.4', '3.5', '3.6', '3.7', '3.8', '3.9', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15-bullseye', '16', '17', '18', '19', '20', 'latest']
    container: silkeh/clang:${{ matrix.compiler }}
    steps:
      - name: Install unzip and git
        run: apt-get update ; apt-get install -y unzip git
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Set env FORCE_STDCPPFS_FLAG for clang 7 / 8 / 9 / 10
        run: echo "JSON_FORCED_GLOBAL_COMPILE_OPTIONS=-DJSON_HAS_FILESYSTEM=0;-DJSON_HAS_EXPERIMENTAL_FILESYSTEM=0" >> "$GITHUB_ENV"
        if: ${{ matrix.compiler == '7' || matrix.compiler == '8' || matrix.compiler == '9' || matrix.compiler == '10' }}
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_compiler_default

  ci_test_standards_gcc:
    runs-on: ubuntu-latest
    container: gcc:latest
    strategy:
      matrix:
        standard: [11, 14, 17, 20, 23, 26]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_test_gcc_cxx${{ matrix.standard }}

  ci_test_standards_clang:
    runs-on: ubuntu-latest
    container: silkeh/clang:latest
    strategy:
      matrix:
        standard: [11, 14, 17, 20, 23, 26]
        stdlib: [libcxx, libstdcxx]
    steps:
      - name: Install git and unzip
        run: apt-get update ; apt-get install -y git unzip
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build with libc++
        run: cmake --build build --target ci_test_clang_libcxx_cxx${{ matrix.standard }}
        if: ${{ matrix.stdlib == 'libcxx' }}
      - name: Build with libstdc++
        run: cmake --build build --target ci_test_clang_cxx${{ matrix.standard }}
        if: ${{ matrix.stdlib == 'libstdcxx' }}

  ci_cuda_example:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.4.0
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_cuda_example

  ci_module_cpp20:
    strategy:
      matrix:
        container: ['gcc:latest', 'silkeh/clang:latest']
    runs-on: ubuntu-latest
    container: ${{ matrix.container }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ci_module_cpp20

  ci_icpc:
    runs-on: ubuntu-latest
    container: ghcr.io/nlohmann/json-ci:v2.2.0
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: |
          . /opt/intel/oneapi/setvars.sh
          cmake --build build --target ci_icpc

  ci_emscripten:
    runs-on: ubuntu-latest
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: Install emscripten
        uses: mymindstorm/setup-emsdk@v14
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Get latest CMake and ninja
        uses: lukka/get-cmake@ea004816823209b8d1211e47b216185caee12cc5 # v4.02
      - name: Run CMake
        run: cmake -S . -B build -DCMAKE_TOOLCHAIN_FILE=$EMSDK/upstream/emscripten/cmake/Modules/Platform/Emscripten.cmake -GNinja
      - name: Build
        run: cmake --build build

  ci_test_documentation:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        target: [ci_test_examples, ci_test_build_documentation]
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Run CMake
        run: cmake -S . -B build -DJSON_CI=On
      - name: Build
        run: cmake --build build --target ${{ matrix.target }}
