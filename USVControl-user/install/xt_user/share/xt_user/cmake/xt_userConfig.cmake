# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_xt_user_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED xt_user_FOUND)
    # explicitly set it to FALSE, otherwise <PERSON><PERSON><PERSON> will set it to TRUE
    set(xt_user_FOUND FALSE)
  elseif(NOT xt_user_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(xt_user_FOUND FALSE)
  endif()
  return()
endif()
set(_xt_user_CONFIG_INCLUDED TRUE)

# output package information
if(NOT xt_user_FIND_QUIETLY)
  message(STATUS "Found xt_user: 0.7.4 (${xt_user_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'xt_user' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${xt_user_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(xt_user_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "ament_cmake_export_dependencies-extras.cmake")
foreach(_extra ${_extras})
  include("${xt_user_DIR}/${_extra}")
endforeach()
