<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format2.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>xt_user</name>
  <version>0.7.4</version>
  <description>plan the trajectory and action of USV</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>
  <author><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <depend>nlohmann_json</depend>
  
  <build_depend>rclcpp</build_depend>
  <build_depend>boat_msgs</build_depend>
  <build_depend>rttest</build_depend>
  <build_depend>tlsf_cpp</build_depend>

  <exec_depend>rclcpp</exec_depend>
  <exec_depend>boat_msgs</exec_depend>
  <exec_depend>rttest</exec_depend>
  <exec_depend>tlsf_cpp</exec_depend>

  <test_depend>ament_cmake_pytest</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>launch</test_depend>
  <test_depend>launch_testing</test_depend>
  <test_depend>launch_testing_ament_cmake</test_depend>
  <test_depend>launch_testing_ros</test_depend>
  <test_depend>rmw_implementation_cmake</test_depend>
  <test_depend>ros2run</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>

