[0.121s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.121s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7fe8d7b1f0d0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fe8d7c605e0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fe8d7c605e0>>)
[0.292s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.293s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.293s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.293s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.293s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.293s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.293s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/project2_new/USVControl-user'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.293s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.294s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.304s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ignore', 'ignore_ament_install']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore_ament_install'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_pkg']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_pkg'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_meta']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_meta'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ros']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ros'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['cmake', 'python']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'cmake'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['python_setup_py']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python_setup_py'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ignore', 'ignore_ament_install']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore_ament_install'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_pkg']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_pkg'
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_meta']
[0.305s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_meta'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ros']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ros'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['cmake', 'python']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'cmake'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['python_setup_py']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python_setup_py'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ignore', 'ignore_ament_install']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore_ament_install'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_pkg']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_pkg'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_meta']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_meta'
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ros']
[0.306s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ros'
[0.309s] DEBUG:colcon.colcon_core.package_identification:Package 'ControlNode/planning' with type 'ros.ament_cmake' and name 'xt_user'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.309s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[0.310s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['ignore', 'ignore_ament_install']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'ignore'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'ignore_ament_install'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['colcon_pkg']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'colcon_pkg'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['colcon_meta']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'colcon_meta'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['ros']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'ros'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['cmake', 'python']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'cmake'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'python'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['python_setup_py']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'python_setup_py'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['ignore', 'ignore_ament_install']
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'ignore'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'ignore_ament_install'
[0.311s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['colcon_pkg']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'colcon_pkg'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['colcon_meta']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'colcon_meta'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['ros']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'ros'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['cmake', 'python']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'cmake'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'python'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['python_setup_py']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'python_setup_py'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['ignore', 'ignore_ament_install']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'ignore'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'ignore_ament_install'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['colcon_pkg']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'colcon_pkg'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['colcon_meta']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'colcon_meta'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['ros']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'ros'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['cmake', 'python']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'cmake'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'python'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['python_setup_py']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'python_setup_py'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['ignore', 'ignore_ament_install']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'ignore'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'ignore_ament_install'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['colcon_pkg']
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'colcon_pkg'
[0.312s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['colcon_meta']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'colcon_meta'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['ros']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'ros'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['cmake', 'python']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'cmake'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'python'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['python_setup_py']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'python_setup_py'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['ignore', 'ignore_ament_install']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'ignore'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'ignore_ament_install'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['colcon_pkg']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'colcon_pkg'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['colcon_meta']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'colcon_meta'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['ros']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'ros'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['cmake', 'python']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'cmake'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'python'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['python_setup_py']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'python_setup_py'
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['ignore', 'ignore_ament_install']
[0.313s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'ignore'
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'ignore_ament_install'
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['colcon_pkg']
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'colcon_pkg'
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['colcon_meta']
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'colcon_meta'
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['ros']
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'ros'
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['cmake', 'python']
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'cmake'
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'python'
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['python_setup_py']
[0.314s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'python_setup_py'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['ignore', 'ignore_ament_install']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'ignore'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'ignore_ament_install'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['colcon_pkg']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'colcon_pkg'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['colcon_meta']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'colcon_meta'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['ros']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'ros'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['cmake', 'python']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'cmake'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'python'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['python_setup_py']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'python_setup_py'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['ignore', 'ignore_ament_install']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'ignore'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'ignore_ament_install'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['colcon_pkg']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'colcon_pkg'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['colcon_meta']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'colcon_meta'
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['ros']
[0.315s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'ros'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['cmake', 'python']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'cmake'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'python'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['python_setup_py']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'python_setup_py'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['ignore', 'ignore_ament_install']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'ignore'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'ignore_ament_install'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['colcon_pkg']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'colcon_pkg'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['colcon_meta']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'colcon_meta'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['ros']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'ros'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['cmake', 'python']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'cmake'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'python'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['python_setup_py']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'python_setup_py'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['ignore', 'ignore_ament_install']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'ignore'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'ignore_ament_install'
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['colcon_pkg']
[0.316s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'colcon_pkg'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['colcon_meta']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'colcon_meta'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['ros']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'ros'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['cmake', 'python']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'cmake'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'python'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['python_setup_py']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'python_setup_py'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['ignore', 'ignore_ament_install']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'ignore'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'ignore_ament_install'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['colcon_pkg']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'colcon_pkg'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['colcon_meta']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'colcon_meta'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['ros']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'ros'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['cmake', 'python']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'cmake'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'python'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['python_setup_py']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'python_setup_py'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['ignore', 'ignore_ament_install']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'ignore'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'ignore_ament_install'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['colcon_pkg']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'colcon_pkg'
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['colcon_meta']
[0.317s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'colcon_meta'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['ros']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'ros'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['cmake', 'python']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'cmake'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'python'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['python_setup_py']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'python_setup_py'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['ignore', 'ignore_ament_install']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'ignore'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'ignore_ament_install'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['colcon_pkg']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'colcon_pkg'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['colcon_meta']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'colcon_meta'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['ros']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'ros'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['cmake', 'python']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'cmake'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'python'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['python_setup_py']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'python_setup_py'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['ignore', 'ignore_ament_install']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'ignore'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'ignore_ament_install'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['colcon_pkg']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'colcon_pkg'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['colcon_meta']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'colcon_meta'
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['ros']
[0.318s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'ros'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['cmake', 'python']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'cmake'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'python'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['python_setup_py']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'python_setup_py'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['ignore', 'ignore_ament_install']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'ignore'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'ignore_ament_install'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['colcon_pkg']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'colcon_pkg'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['colcon_meta']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'colcon_meta'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['ros']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'ros'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['cmake', 'python']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'cmake'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'python'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['python_setup_py']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'python_setup_py'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['ignore', 'ignore_ament_install']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'ignore'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'ignore_ament_install'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['colcon_pkg']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'colcon_pkg'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['colcon_meta']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'colcon_meta'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['ros']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'ros'
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['cmake', 'python']
[0.319s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'cmake'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'python'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['python_setup_py']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'python_setup_py'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['ignore', 'ignore_ament_install']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'ignore'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'ignore_ament_install'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['colcon_pkg']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'colcon_pkg'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['colcon_meta']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'colcon_meta'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['ros']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'ros'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['cmake', 'python']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'cmake'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'python'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['python_setup_py']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'python_setup_py'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['ignore', 'ignore_ament_install']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'ignore'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'ignore_ament_install'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['colcon_pkg']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'colcon_pkg'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['colcon_meta']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'colcon_meta'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['ros']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'ros'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['cmake', 'python']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'cmake'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'python'
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['python_setup_py']
[0.320s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'python_setup_py'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['ignore', 'ignore_ament_install']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'ignore'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'ignore_ament_install'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['colcon_pkg']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'colcon_pkg'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['colcon_meta']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'colcon_meta'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['ros']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'ros'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['cmake', 'python']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'cmake'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'python'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['python_setup_py']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'python_setup_py'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['ignore', 'ignore_ament_install']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'ignore'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'ignore_ament_install'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['colcon_pkg']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'colcon_pkg'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['colcon_meta']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'colcon_meta'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['ros']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'ros'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['cmake', 'python']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'cmake'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'python'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['python_setup_py']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'python_setup_py'
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['ignore', 'ignore_ament_install']
[0.321s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'ignore'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'ignore_ament_install'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['colcon_pkg']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'colcon_pkg'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['colcon_meta']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'colcon_meta'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['ros']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'ros'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['cmake', 'python']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'cmake'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'python'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['python_setup_py']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'python_setup_py'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['ignore', 'ignore_ament_install']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'ignore'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'ignore_ament_install'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['colcon_pkg']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'colcon_pkg'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['colcon_meta']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'colcon_meta'
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['ros']
[0.322s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'ros'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['cmake', 'python']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'cmake'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'python'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['python_setup_py']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'python_setup_py'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['ignore', 'ignore_ament_install']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'ignore'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'ignore_ament_install'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['colcon_pkg']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'colcon_pkg'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['colcon_meta']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'colcon_meta'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['ros']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'ros'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['cmake', 'python']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'cmake'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'python'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['python_setup_py']
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'python_setup_py'
[0.323s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['ignore', 'ignore_ament_install']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'ignore'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'ignore_ament_install'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['colcon_pkg']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'colcon_pkg'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['colcon_meta']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'colcon_meta'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['ros']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'ros'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['cmake', 'python']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'cmake'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'python'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['python_setup_py']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'python_setup_py'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['ignore', 'ignore_ament_install']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'ignore'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'ignore_ament_install'
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['colcon_pkg']
[0.324s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'colcon_pkg'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['colcon_meta']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'colcon_meta'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['ros']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'ros'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['cmake', 'python']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'cmake'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'python'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['python_setup_py']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'python_setup_py'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['ignore', 'ignore_ament_install']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'ignore'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'ignore_ament_install'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['colcon_pkg']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'colcon_pkg'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['colcon_meta']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'colcon_meta'
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['ros']
[0.325s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'ros'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['cmake', 'python']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'cmake'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'python'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['python_setup_py']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'python_setup_py'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['ignore', 'ignore_ament_install']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'ignore'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'ignore_ament_install'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['colcon_pkg']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'colcon_pkg'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['colcon_meta']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'colcon_meta'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['ros']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'ros'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['cmake', 'python']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'cmake'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'python'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['python_setup_py']
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'python_setup_py'
[0.326s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'ignore_ament_install'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['colcon_pkg']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'colcon_pkg'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['colcon_meta']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'colcon_meta'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['ros']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'ros'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['cmake', 'python']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'cmake'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'python'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['python_setup_py']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'python_setup_py'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['ignore', 'ignore_ament_install']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'ignore'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'ignore_ament_install'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['colcon_pkg']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'colcon_pkg'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['colcon_meta']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'colcon_meta'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['ros']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'ros'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['cmake', 'python']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'cmake'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'python'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['python_setup_py']
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'python_setup_py'
[0.327s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['ignore', 'ignore_ament_install']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'ignore'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'ignore_ament_install'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['colcon_pkg']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'colcon_pkg'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['colcon_meta']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'colcon_meta'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['ros']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'ros'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['cmake', 'python']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'cmake'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'python'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['python_setup_py']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'python_setup_py'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['ignore', 'ignore_ament_install']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'ignore'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'ignore_ament_install'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['colcon_pkg']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'colcon_pkg'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['colcon_meta']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'colcon_meta'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['ros']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'ros'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['cmake', 'python']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'cmake'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'python'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['python_setup_py']
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'python_setup_py'
[0.328s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['ignore', 'ignore_ament_install']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'ignore'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'ignore_ament_install'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['colcon_pkg']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'colcon_pkg'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['colcon_meta']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'colcon_meta'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['ros']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'ros'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['cmake', 'python']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'cmake'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'python'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['python_setup_py']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'python_setup_py'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['ignore', 'ignore_ament_install']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'ignore'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'ignore_ament_install'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['colcon_pkg']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'colcon_pkg'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['colcon_meta']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'colcon_meta'
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['ros']
[0.329s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'ros'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['cmake', 'python']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'cmake'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'python'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['python_setup_py']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'python_setup_py'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['ignore', 'ignore_ament_install']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'ignore'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'ignore_ament_install'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['colcon_pkg']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'colcon_pkg'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['colcon_meta']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'colcon_meta'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['ros']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'ros'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['cmake', 'python']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'cmake'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'python'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['python_setup_py']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'python_setup_py'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['ignore', 'ignore_ament_install']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'ignore'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'ignore_ament_install'
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['colcon_pkg']
[0.330s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'colcon_pkg'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['colcon_meta']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'colcon_meta'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['ros']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'ros'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['cmake', 'python']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'cmake'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'python'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['python_setup_py']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'python_setup_py'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['ignore', 'ignore_ament_install']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'ignore'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'ignore_ament_install'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['colcon_pkg']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'colcon_pkg'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['colcon_meta']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'colcon_meta'
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['ros']
[0.331s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'ros'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['cmake', 'python']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'cmake'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'python'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['python_setup_py']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'python_setup_py'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['ignore', 'ignore_ament_install']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'ignore'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'ignore_ament_install'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['colcon_pkg']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'colcon_pkg'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['colcon_meta']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'colcon_meta'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['ros']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'ros'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['cmake', 'python']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'cmake'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'python'
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['python_setup_py']
[0.332s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'python_setup_py'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['ignore', 'ignore_ament_install']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'ignore'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'ignore_ament_install'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['colcon_pkg']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'colcon_pkg'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['colcon_meta']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'colcon_meta'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['ros']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'ros'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['cmake', 'python']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'cmake'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'python'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['python_setup_py']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'python_setup_py'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['ignore', 'ignore_ament_install']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'ignore'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'ignore_ament_install'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['colcon_pkg']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'colcon_pkg'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['colcon_meta']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'colcon_meta'
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['ros']
[0.333s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'ros'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['cmake', 'python']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'cmake'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'python'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['python_setup_py']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'python_setup_py'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['ignore', 'ignore_ament_install']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'ignore'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'ignore_ament_install'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['colcon_pkg']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'colcon_pkg'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['colcon_meta']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'colcon_meta'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['ros']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'ros'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['cmake', 'python']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'cmake'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'python'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['python_setup_py']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'python_setup_py'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['ignore', 'ignore_ament_install']
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'ignore'
[0.334s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'ignore_ament_install'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['colcon_pkg']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'colcon_pkg'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['colcon_meta']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'colcon_meta'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['ros']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'ros'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['cmake', 'python']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'cmake'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'python'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['python_setup_py']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'python_setup_py'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['ignore', 'ignore_ament_install']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'ignore'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'ignore_ament_install'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['colcon_pkg']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'colcon_pkg'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['colcon_meta']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'colcon_meta'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['ros']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'ros'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['cmake', 'python']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'cmake'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'python'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['python_setup_py']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'python_setup_py'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['ignore', 'ignore_ament_install']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'ignore'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'ignore_ament_install'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['colcon_pkg']
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'colcon_pkg'
[0.335s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['colcon_meta']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'colcon_meta'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['ros']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'ros'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['cmake', 'python']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'cmake'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'python'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['python_setup_py']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'python_setup_py'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['ignore', 'ignore_ament_install']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'ignore'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'ignore_ament_install'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['colcon_pkg']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'colcon_pkg'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['colcon_meta']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'colcon_meta'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['ros']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'ros'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['cmake', 'python']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'cmake'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'python'
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['python_setup_py']
[0.336s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'python_setup_py'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['ignore', 'ignore_ament_install']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'ignore'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'ignore_ament_install'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['colcon_pkg']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'colcon_pkg'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['colcon_meta']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'colcon_meta'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['ros']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'ros'
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['cmake', 'python']
[0.337s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'cmake'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'python'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['python_setup_py']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'python_setup_py'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['ignore', 'ignore_ament_install']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'ignore'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'ignore_ament_install'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['colcon_pkg']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'colcon_pkg'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['colcon_meta']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'colcon_meta'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['ros']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'ros'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['cmake', 'python']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'cmake'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'python'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['python_setup_py']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'python_setup_py'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['ignore', 'ignore_ament_install']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'ignore'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'ignore_ament_install'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['colcon_pkg']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'colcon_pkg'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['colcon_meta']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'colcon_meta'
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['ros']
[0.338s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'ros'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['cmake', 'python']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'cmake'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'python'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['python_setup_py']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'python_setup_py'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['ignore', 'ignore_ament_install']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'ignore'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'ignore_ament_install'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['colcon_pkg']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'colcon_pkg'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['colcon_meta']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'colcon_meta'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['ros']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'ros'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['cmake', 'python']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'cmake'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'python'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['python_setup_py']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'python_setup_py'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['ignore', 'ignore_ament_install']
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'ignore'
[0.339s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'ignore_ament_install'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['colcon_pkg']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'colcon_pkg'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['colcon_meta']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'colcon_meta'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['ros']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'ros'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['cmake', 'python']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'cmake'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'python'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['python_setup_py']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'python_setup_py'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'ignore'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'ignore_ament_install'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['colcon_pkg']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'colcon_pkg'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['colcon_meta']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'colcon_meta'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['ros']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'ros'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['cmake', 'python']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'cmake'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'python'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['python_setup_py']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'python_setup_py'
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['ignore', 'ignore_ament_install']
[0.340s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'ignore'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'ignore_ament_install'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['colcon_pkg']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'colcon_pkg'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['colcon_meta']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'colcon_meta'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['ros']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'ros'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['cmake', 'python']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'cmake'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'python'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['python_setup_py']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'python_setup_py'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['ignore', 'ignore_ament_install']
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'ignore'
[0.341s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'ignore_ament_install'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['colcon_pkg']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'colcon_pkg'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['colcon_meta']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'colcon_meta'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['ros']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'ros'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['cmake', 'python']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'cmake'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'python'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['python_setup_py']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'python_setup_py'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['ignore', 'ignore_ament_install']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'ignore'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'ignore_ament_install'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['colcon_pkg']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'colcon_pkg'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['colcon_meta']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'colcon_meta'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['ros']
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'ros'
[0.342s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['cmake', 'python']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'cmake'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'python'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['python_setup_py']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'python_setup_py'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['ignore', 'ignore_ament_install']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'ignore'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'ignore_ament_install'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['colcon_pkg']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'colcon_pkg'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['colcon_meta']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'colcon_meta'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['ros']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'ros'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['cmake', 'python']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'cmake'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'python'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['python_setup_py']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'python_setup_py'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['ignore', 'ignore_ament_install']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'ignore'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'ignore_ament_install'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['colcon_pkg']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'colcon_pkg'
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['colcon_meta']
[0.343s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'colcon_meta'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['ros']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'ros'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['cmake', 'python']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'cmake'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'python'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['python_setup_py']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'python_setup_py'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['ignore', 'ignore_ament_install']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'ignore'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'ignore_ament_install'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['colcon_pkg']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'colcon_pkg'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['colcon_meta']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'colcon_meta'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['ros']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'ros'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['cmake', 'python']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'cmake'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'python'
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['python_setup_py']
[0.344s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'python_setup_py'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['ignore', 'ignore_ament_install']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'ignore'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'ignore_ament_install'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['colcon_pkg']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'colcon_pkg'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['colcon_meta']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'colcon_meta'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['ros']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'ros'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['cmake', 'python']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'cmake'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'python'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['python_setup_py']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'python_setup_py'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['ignore', 'ignore_ament_install']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'ignore'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'ignore_ament_install'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['colcon_pkg']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'colcon_pkg'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['colcon_meta']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'colcon_meta'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['ros']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'ros'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['cmake', 'python']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'cmake'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'python'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['python_setup_py']
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'python_setup_py'
[0.345s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['ignore', 'ignore_ament_install']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'ignore'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'ignore_ament_install'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['colcon_pkg']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'colcon_pkg'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['colcon_meta']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'colcon_meta'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['ros']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'ros'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['cmake', 'python']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'cmake'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'python'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['python_setup_py']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'python_setup_py'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['ignore', 'ignore_ament_install']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'ignore'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'ignore_ament_install'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['colcon_pkg']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'colcon_pkg'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['colcon_meta']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'colcon_meta'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['ros']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'ros'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['cmake', 'python']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'cmake'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'python'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['python_setup_py']
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'python_setup_py'
[0.346s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['ignore', 'ignore_ament_install']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'ignore'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'ignore_ament_install'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['colcon_pkg']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'colcon_pkg'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['colcon_meta']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'colcon_meta'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['ros']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'ros'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['cmake', 'python']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'cmake'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'python'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['python_setup_py']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'python_setup_py'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['ignore', 'ignore_ament_install']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'ignore'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'ignore_ament_install'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['colcon_pkg']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'colcon_pkg'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['colcon_meta']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'colcon_meta'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['ros']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'ros'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['cmake', 'python']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'cmake'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'python'
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['python_setup_py']
[0.347s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'python_setup_py'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['ignore', 'ignore_ament_install']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'ignore'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'ignore_ament_install'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['colcon_pkg']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'colcon_pkg'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['colcon_meta']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'colcon_meta'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['ros']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'ros'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['cmake', 'python']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'cmake'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'python'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['python_setup_py']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'python_setup_py'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['ignore', 'ignore_ament_install']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'ignore'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'ignore_ament_install'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['colcon_pkg']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'colcon_pkg'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['colcon_meta']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'colcon_meta'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['ros']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'ros'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['cmake', 'python']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'cmake'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'python'
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['python_setup_py']
[0.348s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'python_setup_py'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['ignore', 'ignore_ament_install']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'ignore'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'ignore_ament_install'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['colcon_pkg']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'colcon_pkg'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['colcon_meta']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'colcon_meta'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['ros']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'ros'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['cmake', 'python']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'cmake'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'python'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['python_setup_py']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'python_setup_py'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['ignore', 'ignore_ament_install']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'ignore'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'ignore_ament_install'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['colcon_pkg']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'colcon_pkg'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['colcon_meta']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'colcon_meta'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['ros']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'ros'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['cmake', 'python']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'cmake'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'python'
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['python_setup_py']
[0.349s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'python_setup_py'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['ignore', 'ignore_ament_install']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'ignore'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'ignore_ament_install'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['colcon_pkg']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'colcon_pkg'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['colcon_meta']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'colcon_meta'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['ros']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'ros'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['cmake', 'python']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'cmake'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'python'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['python_setup_py']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'python_setup_py'
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['ignore', 'ignore_ament_install']
[0.350s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'ignore'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'ignore_ament_install'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['colcon_pkg']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'colcon_pkg'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['colcon_meta']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'colcon_meta'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['ros']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'ros'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['cmake', 'python']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'cmake'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'python'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['python_setup_py']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'python_setup_py'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['ignore', 'ignore_ament_install']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'ignore'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'ignore_ament_install'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['colcon_pkg']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'colcon_pkg'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['colcon_meta']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'colcon_meta'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['ros']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'ros'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['cmake', 'python']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'cmake'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'python'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['python_setup_py']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'python_setup_py'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['ignore', 'ignore_ament_install']
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'ignore'
[0.351s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'ignore_ament_install'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['colcon_pkg']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'colcon_pkg'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['colcon_meta']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'colcon_meta'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['ros']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'ros'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['cmake', 'python']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'cmake'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'python'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['python_setup_py']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'python_setup_py'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['ignore', 'ignore_ament_install']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'ignore'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'ignore_ament_install'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['colcon_pkg']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'colcon_pkg'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['colcon_meta']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'colcon_meta'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['ros']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'ros'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['cmake', 'python']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'cmake'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'python'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['python_setup_py']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'python_setup_py'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['ignore', 'ignore_ament_install']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'ignore'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'ignore_ament_install'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['colcon_pkg']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'colcon_pkg'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['colcon_meta']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'colcon_meta'
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['ros']
[0.352s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'ros'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['cmake', 'python']
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'cmake'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'python'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['python_setup_py']
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'python_setup_py'
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['ignore', 'ignore_ament_install']
[0.353s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'ignore'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'ignore_ament_install'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['colcon_pkg']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'colcon_pkg'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['colcon_meta']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'colcon_meta'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['ros']
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'ros'
[0.354s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['cmake', 'python']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'cmake'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'python'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['python_setup_py']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'python_setup_py'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['ignore', 'ignore_ament_install']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'ignore'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'ignore_ament_install'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['colcon_pkg']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'colcon_pkg'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['colcon_meta']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'colcon_meta'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['ros']
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'ros'
[0.355s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['cmake', 'python']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'cmake'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'python'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['python_setup_py']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'python_setup_py'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['ignore', 'ignore_ament_install']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'ignore'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'ignore_ament_install'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['colcon_pkg']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'colcon_pkg'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['colcon_meta']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'colcon_meta'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['ros']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'ros'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['cmake', 'python']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'cmake'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'python'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['python_setup_py']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'python_setup_py'
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['ignore', 'ignore_ament_install']
[0.356s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'ignore'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'ignore_ament_install'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['colcon_pkg']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'colcon_pkg'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['colcon_meta']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'colcon_meta'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['ros']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'ros'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['cmake', 'python']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'cmake'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'python'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['python_setup_py']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'python_setup_py'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['ignore', 'ignore_ament_install']
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'ignore'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'ignore_ament_install'
[0.357s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['colcon_pkg']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'colcon_pkg'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['colcon_meta']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'colcon_meta'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['ros']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'ros'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['cmake', 'python']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'cmake'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'python'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['python_setup_py']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'python_setup_py'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['ignore', 'ignore_ament_install']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'ignore'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'ignore_ament_install'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['colcon_pkg']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'colcon_pkg'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['colcon_meta']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'colcon_meta'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['ros']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'ros'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['cmake', 'python']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'cmake'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'python'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['python_setup_py']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'python_setup_py'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['ignore', 'ignore_ament_install']
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'ignore'
[0.358s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'ignore_ament_install'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['colcon_pkg']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'colcon_pkg'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['colcon_meta']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'colcon_meta'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['ros']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'ros'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['cmake', 'python']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'cmake'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'python'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['python_setup_py']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'python_setup_py'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['ignore', 'ignore_ament_install']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'ignore'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'ignore_ament_install'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['colcon_pkg']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'colcon_pkg'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['colcon_meta']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'colcon_meta'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['ros']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'ros'
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['cmake', 'python']
[0.359s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'cmake'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'python'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['python_setup_py']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'python_setup_py'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['ignore', 'ignore_ament_install']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'ignore'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'ignore_ament_install'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['colcon_pkg']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'colcon_pkg'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['colcon_meta']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'colcon_meta'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['ros']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'ros'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['cmake', 'python']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'cmake'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'python'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['python_setup_py']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'python_setup_py'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['ignore', 'ignore_ament_install']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'ignore'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'ignore_ament_install'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['colcon_pkg']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'colcon_pkg'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['colcon_meta']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'colcon_meta'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['ros']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'ros'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['cmake', 'python']
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'cmake'
[0.360s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'python'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['python_setup_py']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'python_setup_py'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['ignore', 'ignore_ament_install']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'ignore'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'ignore_ament_install'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['colcon_pkg']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'colcon_pkg'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['colcon_meta']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'colcon_meta'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['ros']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'ros'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['cmake', 'python']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'cmake'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'python'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['python_setup_py']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'python_setup_py'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['ignore', 'ignore_ament_install']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'ignore'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'ignore_ament_install'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['colcon_pkg']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'colcon_pkg'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['colcon_meta']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'colcon_meta'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['ros']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'ros'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['cmake', 'python']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'cmake'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'python'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['python_setup_py']
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'python_setup_py'
[0.361s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['ignore', 'ignore_ament_install']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'ignore'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'ignore_ament_install'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['colcon_pkg']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'colcon_pkg'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['colcon_meta']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'colcon_meta'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['ros']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'ros'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['cmake', 'python']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'cmake'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'python'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['python_setup_py']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'python_setup_py'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['ignore', 'ignore_ament_install']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'ignore'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'ignore_ament_install'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['colcon_pkg']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'colcon_pkg'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['colcon_meta']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'colcon_meta'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['ros']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'ros'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['cmake', 'python']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'cmake'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'python'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['python_setup_py']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'python_setup_py'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['ignore', 'ignore_ament_install']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'ignore'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'ignore_ament_install'
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['colcon_pkg']
[0.362s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'colcon_pkg'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['colcon_meta']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'colcon_meta'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['ros']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'ros'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['cmake', 'python']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'cmake'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'python'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['python_setup_py']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'python_setup_py'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['ignore', 'ignore_ament_install']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'ignore'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'ignore_ament_install'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['colcon_pkg']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'colcon_pkg'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['colcon_meta']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'colcon_meta'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['ros']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'ros'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['cmake', 'python']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'cmake'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'python'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['python_setup_py']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'python_setup_py'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['ignore', 'ignore_ament_install']
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'ignore'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'ignore_ament_install'
[0.363s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['colcon_pkg']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'colcon_pkg'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['colcon_meta']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'colcon_meta'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['ros']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'ros'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['cmake', 'python']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'cmake'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'python'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['python_setup_py']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'python_setup_py'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['ignore', 'ignore_ament_install']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'ignore'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'ignore_ament_install'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['colcon_pkg']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'colcon_pkg'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['colcon_meta']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'colcon_meta'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['ros']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'ros'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['cmake', 'python']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'cmake'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'python'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['python_setup_py']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'python_setup_py'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['ignore', 'ignore_ament_install']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'ignore'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'ignore_ament_install'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['colcon_pkg']
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'colcon_pkg'
[0.364s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['colcon_meta']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'colcon_meta'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['ros']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'ros'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['cmake', 'python']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'cmake'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'python'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['python_setup_py']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'python_setup_py'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['ignore', 'ignore_ament_install']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'ignore'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'ignore_ament_install'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['colcon_pkg']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'colcon_pkg'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['colcon_meta']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'colcon_meta'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['ros']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'ros'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['cmake', 'python']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'cmake'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'python'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['python_setup_py']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'python_setup_py'
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['ignore', 'ignore_ament_install']
[0.365s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'ignore'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'ignore_ament_install'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['colcon_pkg']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'colcon_pkg'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['colcon_meta']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'colcon_meta'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['ros']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'ros'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['cmake', 'python']
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'cmake'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'python'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['python_setup_py']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'python_setup_py'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['ignore', 'ignore_ament_install']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'ignore'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'ignore_ament_install'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['colcon_pkg']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'colcon_pkg'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['colcon_meta']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'colcon_meta'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['ros']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'ros'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['cmake', 'python']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'cmake'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'python'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['python_setup_py']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'python_setup_py'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['ignore', 'ignore_ament_install']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'ignore'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'ignore_ament_install'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['colcon_pkg']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'colcon_pkg'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['colcon_meta']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'colcon_meta'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['ros']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'ros'
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['cmake', 'python']
[0.368s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'cmake'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'python'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['python_setup_py']
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'python_setup_py'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['ignore', 'ignore_ament_install']
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'ignore'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'ignore_ament_install'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['colcon_pkg']
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'colcon_pkg'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['colcon_meta']
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'colcon_meta'
[0.369s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['ros']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'ros'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['cmake', 'python']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'cmake'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'python'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['python_setup_py']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'python_setup_py'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['ignore', 'ignore_ament_install']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'ignore'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'ignore_ament_install'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['colcon_pkg']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'colcon_pkg'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['colcon_meta']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'colcon_meta'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['ros']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'ros'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['cmake', 'python']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'cmake'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'python'
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['python_setup_py']
[0.370s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'python_setup_py'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['ignore', 'ignore_ament_install']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'ignore'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'ignore_ament_install'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['colcon_pkg']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'colcon_pkg'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['colcon_meta']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'colcon_meta'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['ros']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'ros'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['cmake', 'python']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'cmake'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'python'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['python_setup_py']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'python_setup_py'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['ignore', 'ignore_ament_install']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'ignore'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'ignore_ament_install'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['colcon_pkg']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'colcon_pkg'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['colcon_meta']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'colcon_meta'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['ros']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'ros'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['cmake', 'python']
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'cmake'
[0.371s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'python'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['python_setup_py']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'python_setup_py'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['ignore', 'ignore_ament_install']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'ignore'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'ignore_ament_install'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['colcon_pkg']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'colcon_pkg'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['colcon_meta']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'colcon_meta'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['ros']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'ros'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['cmake', 'python']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'cmake'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'python'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['python_setup_py']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'python_setup_py'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['ignore', 'ignore_ament_install']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'ignore'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'ignore_ament_install'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['colcon_pkg']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'colcon_pkg'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['colcon_meta']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'colcon_meta'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['ros']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'ros'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['cmake', 'python']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'cmake'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'python'
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['python_setup_py']
[0.372s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'python_setup_py'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['ignore', 'ignore_ament_install']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'ignore'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'ignore_ament_install'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['colcon_pkg']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'colcon_pkg'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['colcon_meta']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'colcon_meta'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['ros']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'ros'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['cmake', 'python']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'cmake'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'python'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['python_setup_py']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'python_setup_py'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['ignore', 'ignore_ament_install']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'ignore'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'ignore_ament_install'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['colcon_pkg']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'colcon_pkg'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['colcon_meta']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'colcon_meta'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['ros']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'ros'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['cmake', 'python']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'cmake'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'python'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['python_setup_py']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'python_setup_py'
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['ignore', 'ignore_ament_install']
[0.373s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'ignore'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'ignore_ament_install'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['colcon_pkg']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'colcon_pkg'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['colcon_meta']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'colcon_meta'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['ros']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'ros'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['cmake', 'python']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'cmake'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'python'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['python_setup_py']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'python_setup_py'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['ignore', 'ignore_ament_install']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'ignore'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'ignore_ament_install'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['colcon_pkg']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'colcon_pkg'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['colcon_meta']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'colcon_meta'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['ros']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'ros'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['cmake', 'python']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'cmake'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'python'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['python_setup_py']
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'python_setup_py'
[0.374s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['ignore', 'ignore_ament_install']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'ignore'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'ignore_ament_install'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['colcon_pkg']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'colcon_pkg'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['colcon_meta']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'colcon_meta'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['ros']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'ros'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['cmake', 'python']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'cmake'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'python'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['python_setup_py']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'python_setup_py'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['ignore', 'ignore_ament_install']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'ignore'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'ignore_ament_install'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['colcon_pkg']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'colcon_pkg'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['colcon_meta']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'colcon_meta'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['ros']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'ros'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['cmake', 'python']
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'cmake'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'python'
[0.375s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['python_setup_py']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'python_setup_py'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['ignore', 'ignore_ament_install']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'ignore'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'ignore_ament_install'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['colcon_pkg']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'colcon_pkg'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['colcon_meta']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'colcon_meta'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['ros']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'ros'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['cmake', 'python']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'cmake'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'python'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['python_setup_py']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'python_setup_py'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['ignore', 'ignore_ament_install']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'ignore'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'ignore_ament_install'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['colcon_pkg']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'colcon_pkg'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['colcon_meta']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'colcon_meta'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['ros']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'ros'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['cmake', 'python']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'cmake'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'python'
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['python_setup_py']
[0.376s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'python_setup_py'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['ignore', 'ignore_ament_install']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'ignore'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'ignore_ament_install'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['colcon_pkg']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'colcon_pkg'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['colcon_meta']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'colcon_meta'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['ros']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'ros'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['cmake', 'python']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'cmake'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'python'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['python_setup_py']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'python_setup_py'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['ignore', 'ignore_ament_install']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'ignore'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'ignore_ament_install'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['colcon_pkg']
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'colcon_pkg'
[0.377s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['colcon_meta']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'colcon_meta'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['ros']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'ros'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['cmake', 'python']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'cmake'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'python'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['python_setup_py']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'python_setup_py'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['ignore', 'ignore_ament_install']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'ignore'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'ignore_ament_install'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['colcon_pkg']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'colcon_pkg'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['colcon_meta']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'colcon_meta'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['ros']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'ros'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['cmake', 'python']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'cmake'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'python'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['python_setup_py']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'python_setup_py'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['ignore', 'ignore_ament_install']
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'ignore'
[0.378s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'ignore_ament_install'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['colcon_pkg']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'colcon_pkg'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['colcon_meta']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'colcon_meta'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['ros']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'ros'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['cmake', 'python']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'cmake'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'python'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['python_setup_py']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'python_setup_py'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['ignore', 'ignore_ament_install']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'ignore'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'ignore_ament_install'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['colcon_pkg']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'colcon_pkg'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['colcon_meta']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'colcon_meta'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['ros']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'ros'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['cmake', 'python']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'cmake'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'python'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['python_setup_py']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'python_setup_py'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['ignore', 'ignore_ament_install']
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'ignore'
[0.379s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'ignore_ament_install'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['colcon_pkg']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'colcon_pkg'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['colcon_meta']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'colcon_meta'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['ros']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'ros'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['cmake', 'python']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'cmake'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'python'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['python_setup_py']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'python_setup_py'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['ignore', 'ignore_ament_install']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'ignore'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'ignore_ament_install'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['colcon_pkg']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'colcon_pkg'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['colcon_meta']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'colcon_meta'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['ros']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'ros'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['cmake', 'python']
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'cmake'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'python'
[0.380s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['python_setup_py']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'python_setup_py'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['ignore', 'ignore_ament_install']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'ignore'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'ignore_ament_install'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['colcon_pkg']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'colcon_pkg'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['colcon_meta']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'colcon_meta'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['ros']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'ros'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['cmake', 'python']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'cmake'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'python'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['python_setup_py']
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'python_setup_py'
[0.381s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['ignore', 'ignore_ament_install']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'ignore'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'ignore_ament_install'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['colcon_pkg']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'colcon_pkg'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['colcon_meta']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'colcon_meta'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['ros']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'ros'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['cmake', 'python']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'cmake'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'python'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['python_setup_py']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'python_setup_py'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['ignore', 'ignore_ament_install']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'ignore'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'ignore_ament_install'
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['colcon_pkg']
[0.382s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'colcon_pkg'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['colcon_meta']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'colcon_meta'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['ros']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'ros'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['cmake', 'python']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'cmake'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'python'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['python_setup_py']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'python_setup_py'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['ignore', 'ignore_ament_install']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'ignore'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'ignore_ament_install'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['colcon_pkg']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'colcon_pkg'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['colcon_meta']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'colcon_meta'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['ros']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'ros'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['cmake', 'python']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'cmake'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'python'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['python_setup_py']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'python_setup_py'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['ignore', 'ignore_ament_install']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'ignore'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'ignore_ament_install'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['colcon_pkg']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'colcon_pkg'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['colcon_meta']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'colcon_meta'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['ros']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'ros'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['cmake', 'python']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'cmake'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'python'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['python_setup_py']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'python_setup_py'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['ignore', 'ignore_ament_install']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'ignore'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'ignore_ament_install'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['colcon_pkg']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'colcon_pkg'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['colcon_meta']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'colcon_meta'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['ros']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'ros'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['cmake', 'python']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'cmake'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'python'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['python_setup_py']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'python_setup_py'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['ignore', 'ignore_ament_install']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'ignore'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'ignore_ament_install'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['colcon_pkg']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'colcon_pkg'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['colcon_meta']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'colcon_meta'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['ros']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'ros'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['cmake', 'python']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'cmake'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'python'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['python_setup_py']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'python_setup_py'
[0.385s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.385s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.385s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.385s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.385s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.403s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.403s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.406s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 271 installed packages in /opt/ros/humble
[0.407s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.446s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_args' from command line to 'None'
[0.446s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target' from command line to 'None'
[0.446s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.447s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_cache' from command line to 'False'
[0.447s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_first' from command line to 'False'
[0.447s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_force_configure' from command line to 'False'
[0.447s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'ament_cmake_args' from command line to 'None'
[0.447s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_cmake_args' from command line to 'None'
[0.447s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.447s] DEBUG:colcon.colcon_core.verb:Building package 'xt_user' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/project2_new/USVControl-user/build/xt_user', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/project2_new/USVControl-user/install/xt_user', 'merge_install': False, 'path': '/home/<USER>/project2_new/USVControl-user/ControlNode/planning', 'symlink_install': False, 'test_result_base': None}
[0.447s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.448s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.449s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/project2_new/USVControl-user/ControlNode/planning' with build type 'ament_cmake'
[0.449s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/project2_new/USVControl-user/ControlNode/planning'
[0.451s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.451s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.451s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.461s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/project2_new/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new/USVControl-user/build/xt_user -- -j8 -l8
[17.814s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/project2_new/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new/USVControl-user/build/xt_user -- -j8 -l8
[17.829s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/project2_new/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/project2_new/USVControl-user/build/xt_user
[17.844s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[17.844s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/project2_new/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/project2_new/USVControl-user/build/xt_user
[17.846s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user' for CMake module files
[17.847s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user' for CMake config files
[17.847s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[17.847s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[17.848s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[17.849s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[17.850s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/lib'
[17.850s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[17.850s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[17.851s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[17.851s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[17.852s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/bin'
[17.852s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[17.853s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[17.854s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/bin'
[17.854s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[17.855s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[17.856s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.sh'
[17.857s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.bash'
[17.858s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[17.859s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/project2_new/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[17.860s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[17.860s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user' for CMake module files
[17.861s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user' for CMake config files
[17.862s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[17.862s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[17.863s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[17.864s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[17.864s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/lib'
[17.865s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[17.865s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[17.866s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[17.866s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[17.868s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/bin'
[17.868s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[17.868s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[17.869s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new/USVControl-user/install/xt_user/bin'
[17.870s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[17.871s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[17.872s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.sh'
[17.872s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.bash'
[17.873s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[17.873s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/project2_new/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[17.874s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[17.874s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[17.874s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[17.874s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[17.879s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[17.879s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[17.879s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[17.897s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[17.897s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/project2_new/USVControl-user/install/local_setup.ps1'
[17.899s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/project2_new/USVControl-user/install/_local_setup_util_ps1.py'
[17.900s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/project2_new/USVControl-user/install/setup.ps1'
[17.902s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/project2_new/USVControl-user/install/local_setup.sh'
[17.903s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/project2_new/USVControl-user/install/_local_setup_util_sh.py'
[17.904s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/project2_new/USVControl-user/install/setup.sh'
[17.905s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/project2_new/USVControl-user/install/local_setup.bash'
[17.905s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/project2_new/USVControl-user/install/setup.bash'
[17.906s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/project2_new/USVControl-user/install/local_setup.zsh'
[17.907s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/project2_new/USVControl-user/install/setup.zsh'
