[0.000000] (-) TimerEvent: {}
[0.000734] (xt_user) JobQueued: {'identifier': 'xt_user', 'dependencies': OrderedDict()}
[0.000802] (xt_user) JobStarted: {'identifier': 'xt_user'}
[0.007068] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'cmake'}
[0.007656] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'build'}
[0.008762] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/project2_new/USVControl-user/build/xt_user', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/project2_new/USVControl-user/build/xt_user', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/project2_new/USVControl-user'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1641'), ('SYSTEMD_EXEC_PID', '1819'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '5901'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:26754'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1784,unix/xumj-virtual-machine:/tmp/.ICE-unix/1784'), ('INVOCATION_ID', '01346c58a1014ccf92e5353cff7841ee'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.RFXTA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-6d8174bbac.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/project2_new/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.065370] (xt_user) StdoutLine: {'line': b'[ 14%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o\x1b[0m\n'}
[0.099587] (-) TimerEvent: {}
[0.201337] (-) TimerEvent: {}
[0.301791] (-) TimerEvent: {}
[0.385354] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[KMotionPlan::MotionPlan()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.385608] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:35:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kvoid* memset(void*, int, size_t)\x1b[m\x1b[K\xe2\x80\x99 clearing an object of type \xe2\x80\x98\x1b[01m\x1b[Kstruct ObstacleAvoidanceParam\x1b[m\x1b[K\xe2\x80\x99 with no trivial copy-assignment; use assignment or value-initialization instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wclass-memaccess\x07-Wclass-memaccess\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.385654] (xt_user) StderrLine: {'line': b'   35 |         \x1b[01;35m\x1b[Kmemset(&obstacleAvoidance, 0, sizeof(ObstacleAvoidanceParam))\x1b[m\x1b[K;\n'}
[0.385692] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.385729] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[0.385795] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:48:1:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstruct ObstacleAvoidanceParam\x1b[m\x1b[K\xe2\x80\x99 declared here\n'}
[0.385835] (xt_user) StderrLine: {'line': b'   48 | \x1b[01;36m\x1b[K{\x1b[m\x1b[K\n'}
[0.385872] (xt_user) StderrLine: {'line': b'      | \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[0.389387] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.389590] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:260:43:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kboatstate\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.389635] (xt_user) StderrLine: {'line': b'  260 | void MotionPlan::TrajecotryGet(\x1b[01;35m\x1b[KTransState boatstate\x1b[m\x1b[K, anchor_mission_control task)\n'}
[0.389673] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;35m\x1b[K~~~~~~~~~~~^~~~~~~~~\x1b[m\x1b[K\n'}
[0.389720] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.389763] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:293:47:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[0]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.389857] (xt_user) StderrLine: {'line': b'  293 |         float pos[2] = { \x1b[01;35m\x1b[Kboatstate.PostoHome[0]\x1b[m\x1b[K, boatstate.PostoHome[1] };\n'}
[0.389896] (xt_user) StderrLine: {'line': b'      |                          \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[0.389932] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:293:71:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[1]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.389977] (xt_user) StderrLine: {'line': b'  293 |         float pos[2] = { boatstate.PostoHome[0], \x1b[01;35m\x1b[Kboatstate.PostoHome[1]\x1b[m\x1b[K };\n'}
[0.390016] (xt_user) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[0.390283] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:368:35:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kunsigned int\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.390392] (xt_user) StderrLine: {'line': b'  368 |     if (\x1b[01;35m\x1b[KdotTrackingParam.TrackNum > phaseTransitionFlag\x1b[m\x1b[K) {\n'}
[0.390437] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.396959] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::CalculateAvoidanceAngle(float*, float*, float)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.397296] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:478:49:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.397402] (xt_user) StderrLine: {'line': b'  478 | float MotionPlan::CalculateAvoidanceAngle(\x1b[01;35m\x1b[Kfloat currentPos[2]\x1b[m\x1b[K, float targetPos[2], float originalAngle)\n'}
[0.397463] (xt_user) StderrLine: {'line': b'      |                                           \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.397515] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:478:70:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KtargetPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.397603] (xt_user) StderrLine: {'line': b'  478 | float MotionPlan::CalculateAvoidanceAngle(float currentPos[2], \x1b[01;35m\x1b[Kfloat targetPos[2]\x1b[m\x1b[K, float originalAngle)\n'}
[0.397722] (xt_user) StderrLine: {'line': b'      |                                                                \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.397805] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool MotionPlan::CheckMissionFailure(float*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.397864] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:510:44:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.397907] (xt_user) StderrLine: {'line': b'  510 | bool MotionPlan::CheckMissionFailure(\x1b[01;35m\x1b[Kfloat currentPos[2]\x1b[m\x1b[K)\n'}
[0.398005] (xt_user) StderrLine: {'line': b'      |                                      \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.402508] (-) TimerEvent: {}
[0.503264] (-) TimerEvent: {}
[0.587708] (xt_user) StdoutLine: {'line': b'[ 28%] \x1b[32m\x1b[1mLinking CXX shared library libplanning_hpp.so\x1b[0m\n'}
[0.603477] (-) TimerEvent: {}
[0.704661] (-) TimerEvent: {}
[0.717143] (xt_user) StdoutLine: {'line': b'[ 71%] Built target planning_hpp\n'}
[0.748936] (xt_user) StdoutLine: {'line': b'[ 85%] \x1b[32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o\x1b[0m\n'}
[0.806270] (-) TimerEvent: {}
[0.907341] (-) TimerEvent: {}
[1.008310] (-) TimerEvent: {}
[1.110078] (-) TimerEvent: {}
[1.210517] (-) TimerEvent: {}
[1.311593] (-) TimerEvent: {}
[1.413064] (-) TimerEvent: {}
[1.513879] (-) TimerEvent: {}
[1.615344] (-) TimerEvent: {}
[1.716737] (-) TimerEvent: {}
[1.818142] (-) TimerEvent: {}
[1.918917] (-) TimerEvent: {}
[2.020329] (-) TimerEvent: {}
[2.121843] (-) TimerEvent: {}
[2.223717] (-) TimerEvent: {}
[2.324950] (-) TimerEvent: {}
[2.425697] (-) TimerEvent: {}
[2.527495] (-) TimerEvent: {}
[2.628453] (-) TimerEvent: {}
[2.728979] (-) TimerEvent: {}
[2.830818] (-) TimerEvent: {}
[2.932010] (-) TimerEvent: {}
[3.032907] (-) TimerEvent: {}
[3.133501] (-) TimerEvent: {}
[3.235455] (-) TimerEvent: {}
[3.337490] (-) TimerEvent: {}
[3.439281] (-) TimerEvent: {}
[3.540066] (-) TimerEvent: {}
[3.641704] (-) TimerEvent: {}
[3.742295] (-) TimerEvent: {}
[3.843293] (-) TimerEvent: {}
[3.945025] (-) TimerEvent: {}
[4.045553] (-) TimerEvent: {}
[4.146097] (-) TimerEvent: {}
[4.247897] (-) TimerEvent: {}
[4.349513] (-) TimerEvent: {}
[4.450916] (-) TimerEvent: {}
[4.552682] (-) TimerEvent: {}
[4.653888] (-) TimerEvent: {}
[4.754607] (-) TimerEvent: {}
[4.855089] (-) TimerEvent: {}
[4.955921] (-) TimerEvent: {}
[4.989361] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[4.989812] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:260:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.989915] (xt_user) StderrLine: {'line': b'  260 |     double \x1b[01;35m\x1b[Kstime\x1b[m\x1b[K, etime;\n'}
[4.989982] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[4.990045] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:260:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Ketime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.990111] (xt_user) StderrLine: {'line': b'  260 |     double stime, \x1b[01;35m\x1b[Ketime\x1b[m\x1b[K;\n'}
[4.990173] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[4.990235] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid* MapShow(void*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[4.990300] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.990366] (xt_user) StderrLine: {'line': b'  304 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K,end;\n'}
[4.990428] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[4.990487] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:18:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.990553] (xt_user) StderrLine: {'line': b'  304 |     double start,\x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[4.990612] (xt_user) StderrLine: {'line': b'      |                  \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[4.990670] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:305:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.990735] (xt_user) StderrLine: {'line': b'  305 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[4.990787] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[4.990837] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:307:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kperiod\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.990896] (xt_user) StderrLine: {'line': b'  307 |     unsigned char \x1b[01;35m\x1b[Kperiod\x1b[m\x1b[K = 0;\n'}
[4.990971] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[4.991030] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:302:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Karg\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[4.991116] (xt_user) StderrLine: {'line': b'  302 | void* MapShow(\x1b[01;35m\x1b[Kvoid *arg\x1b[m\x1b[K)\n'}
[4.991253] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K~~~~~~^~~\x1b[m\x1b[K\n'}
[5.011949] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[5.012707] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:357:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.012888] (xt_user) StderrLine: {'line': b'  357 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kcommand = wp["command"].get<uint16_t>(),\n'}
[5.012953] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.019987] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:358:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.020187] (xt_user) StderrLine: {'line': b'  358 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kseq = wp["seq"].get<uint8_t>(),\n'}
[5.020229] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.026380] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:359:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.026654] (xt_user) StderrLine: {'line': b'  359 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam1 = wp["param1"].get<float>(),\n'}
[5.026723] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.032993] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:360:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.033233] (xt_user) StderrLine: {'line': b'  360 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam2 = wp["param2"].get<float>(),\n'}
[5.033306] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.033507] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:361:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.033571] (xt_user) StderrLine: {'line': b'  361 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam3 = wp["param3"].get<float>(),\n'}
[5.033610] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.033645] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:362:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.033683] (xt_user) StderrLine: {'line': b'  362 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam4 = wp["param4"].get<float>(),\n'}
[5.033727] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.033879] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:363:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.033946] (xt_user) StderrLine: {'line': b'  363 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klat = wp["x"].get<float>(),\n'}
[5.033983] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.034534] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:364:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.034621] (xt_user) StderrLine: {'line': b'  364 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klon = wp["y"].get<float>(),\n'}
[5.034662] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.034768] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:365:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.034835] (xt_user) StderrLine: {'line': b'  365 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kalt = wp["z"].get<float>()\n'}
[5.034874] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.035137] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:366:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kmissing initializer for member \xe2\x80\x98\x1b[01m\x1b[KMissionItem::reserve\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers\x07-Wmissing-field-initializers\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.035219] (xt_user) StderrLine: {'line': b'  366 |             \x1b[01;35m\x1b[K}\x1b[m\x1b[K;\n'}
[5.035258] (xt_user) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.036556] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:379:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<MissionItem>::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.036738] (xt_user) StderrLine: {'line': b'  379 |     for(int i = 0; \x1b[01;35m\x1b[Ki<missions.size()\x1b[m\x1b[K; i++){\n'}
[5.036802] (xt_user) StderrLine: {'line': b'      |                    \x1b[01;35m\x1b[K~^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.056808] (-) TimerEvent: {}
[5.144153] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:703:60:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunsigned conversion from \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Ku_int8_t\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kunsigned char\x1b[m\x1b[K\xe2\x80\x99} changes value from \xe2\x80\x98\x1b[01m\x1b[K512\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[K0\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow\x07-Woverflow\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.144406] (xt_user) StderrLine: {'line': b'  703 |                     taskAnchorMissionControl.mission_num = \x1b[01;35m\x1b[K512\x1b[m\x1b[K;\n'}
[5.144460] (xt_user) StderrLine: {'line': b'      |                                                            \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[5.151584] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:383:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.151823] (xt_user) StderrLine: {'line': b'  383 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K, end;\n'}
[5.151876] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[5.151925] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:383:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.151977] (xt_user) StderrLine: {'line': b'  383 |     double start, \x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[5.152020] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[5.152107] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:384:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kduration\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.152168] (xt_user) StderrLine: {'line': b'  384 |     double \x1b[01;35m\x1b[Kduration\x1b[m\x1b[K[10];\n'}
[5.152224] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[5.152261] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:385:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.152303] (xt_user) StderrLine: {'line': b'  385 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[5.152337] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[5.152389] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:427:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kanglevel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.152427] (xt_user) StderrLine: {'line': b'  427 |     double \x1b[01;35m\x1b[Kanglevel\x1b[m\x1b[K[2];\n'}
[5.152462] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[5.157227] (-) TimerEvent: {}
[5.258848] (-) TimerEvent: {}
[5.360909] (-) TimerEvent: {}
[5.461578] (-) TimerEvent: {}
[5.563203] (-) TimerEvent: {}
[5.663765] (-) TimerEvent: {}
[5.764728] (-) TimerEvent: {}
[5.866478] (-) TimerEvent: {}
[5.966767] (-) TimerEvent: {}
[6.068647] (-) TimerEvent: {}
[6.169615] (-) TimerEvent: {}
[6.270514] (-) TimerEvent: {}
[6.371137] (-) TimerEvent: {}
[6.471977] (-) TimerEvent: {}
[6.572727] (-) TimerEvent: {}
[6.673854] (-) TimerEvent: {}
[6.774692] (-) TimerEvent: {}
[6.876477] (-) TimerEvent: {}
[6.977631] (-) TimerEvent: {}
[7.079050] (-) TimerEvent: {}
[7.179758] (-) TimerEvent: {}
[7.280279] (-) TimerEvent: {}
[7.380870] (-) TimerEvent: {}
[7.481646] (-) TimerEvent: {}
[7.581997] (-) TimerEvent: {}
[7.683199] (-) TimerEvent: {}
[7.784168] (-) TimerEvent: {}
[7.885505] (-) TimerEvent: {}
[7.987355] (-) TimerEvent: {}
[8.088384] (-) TimerEvent: {}
[8.189102] (-) TimerEvent: {}
[8.290124] (-) TimerEvent: {}
[8.390887] (-) TimerEvent: {}
[8.491854] (-) TimerEvent: {}
[8.593594] (-) TimerEvent: {}
[8.695476] (-) TimerEvent: {}
[8.797101] (-) TimerEvent: {}
[8.898483] (-) TimerEvent: {}
[8.999004] (-) TimerEvent: {}
[9.100360] (-) TimerEvent: {}
[9.200782] (-) TimerEvent: {}
[9.302292] (-) TimerEvent: {}
[9.403253] (-) TimerEvent: {}
[9.503678] (-) TimerEvent: {}
[9.605288] (-) TimerEvent: {}
[9.705922] (-) TimerEvent: {}
[9.806873] (-) TimerEvent: {}
[9.907960] (-) TimerEvent: {}
[10.009457] (-) TimerEvent: {}
[10.110299] (-) TimerEvent: {}
[10.210944] (-) TimerEvent: {}
[10.312317] (-) TimerEvent: {}
[10.413539] (-) TimerEvent: {}
[10.515650] (-) TimerEvent: {}
[10.616135] (-) TimerEvent: {}
[10.717128] (-) TimerEvent: {}
[10.818502] (-) TimerEvent: {}
[10.919300] (-) TimerEvent: {}
[11.020109] (-) TimerEvent: {}
[11.121048] (-) TimerEvent: {}
[11.222545] (-) TimerEvent: {}
[11.323638] (-) TimerEvent: {}
[11.425366] (-) TimerEvent: {}
[11.526323] (-) TimerEvent: {}
[11.628248] (-) TimerEvent: {}
[11.728714] (-) TimerEvent: {}
[11.830567] (-) TimerEvent: {}
[11.931014] (-) TimerEvent: {}
[12.032280] (-) TimerEvent: {}
[12.132778] (-) TimerEvent: {}
[12.234594] (-) TimerEvent: {}
[12.335431] (-) TimerEvent: {}
[12.436089] (-) TimerEvent: {}
[12.537945] (-) TimerEvent: {}
[12.639297] (-) TimerEvent: {}
[12.741310] (-) TimerEvent: {}
[12.842340] (-) TimerEvent: {}
[12.943555] (-) TimerEvent: {}
[13.044795] (-) TimerEvent: {}
[13.146674] (-) TimerEvent: {}
[13.247236] (-) TimerEvent: {}
[13.349003] (-) TimerEvent: {}
[13.450208] (-) TimerEvent: {}
[13.551627] (-) TimerEvent: {}
[13.652661] (-) TimerEvent: {}
[13.754342] (-) TimerEvent: {}
[13.855652] (-) TimerEvent: {}
[13.956501] (-) TimerEvent: {}
[14.057946] (-) TimerEvent: {}
[14.158488] (-) TimerEvent: {}
[14.259236] (-) TimerEvent: {}
[14.361210] (-) TimerEvent: {}
[14.461931] (-) TimerEvent: {}
[14.564056] (-) TimerEvent: {}
[14.666183] (-) TimerEvent: {}
[14.766645] (-) TimerEvent: {}
[14.868516] (-) TimerEvent: {}
[14.969675] (-) TimerEvent: {}
[15.070358] (-) TimerEvent: {}
[15.172134] (-) TimerEvent: {}
[15.273461] (-) TimerEvent: {}
[15.373979] (-) TimerEvent: {}
[15.476389] (-) TimerEvent: {}
[15.577848] (-) TimerEvent: {}
[15.679479] (-) TimerEvent: {}
[15.780676] (-) TimerEvent: {}
[15.882243] (-) TimerEvent: {}
[15.984039] (-) TimerEvent: {}
[16.085589] (-) TimerEvent: {}
[16.186121] (-) TimerEvent: {}
[16.257929] (xt_user) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable xt_user_node\x1b[0m\n'}
[16.286365] (-) TimerEvent: {}
[16.387124] (-) TimerEvent: {}
[16.487798] (-) TimerEvent: {}
[16.589746] (-) TimerEvent: {}
[16.691306] (-) TimerEvent: {}
[16.793162] (-) TimerEvent: {}
[16.894658] (-) TimerEvent: {}
[16.996205] (-) TimerEvent: {}
[17.097789] (-) TimerEvent: {}
[17.198542] (-) TimerEvent: {}
[17.299159] (-) TimerEvent: {}
[17.350299] (xt_user) StdoutLine: {'line': b'[100%] Built target xt_user_node\n'}
[17.365130] (xt_user) CommandEnded: {'returncode': 0}
[17.366345] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'install'}
[17.379831] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/project2_new/USVControl-user/build/xt_user'], 'cwd': '/home/<USER>/project2_new/USVControl-user/build/xt_user', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/project2_new/USVControl-user'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1641'), ('SYSTEMD_EXEC_PID', '1819'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '5901'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:26754'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1784,unix/xumj-virtual-machine:/tmp/.ICE-unix/1784'), ('INVOCATION_ID', '01346c58a1014ccf92e5353cff7841ee'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.RFXTA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-6d8174bbac.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/project2_new/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[17.387101] (xt_user) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[17.387362] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so\n'}
[17.387534] (xt_user) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so" to ""\n'}
[17.387898] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node\n'}
[17.392777] (xt_user) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node" to ""\n'}
[17.393098] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user\n'}
[17.393270] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user\n'}
[17.393326] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh\n'}
[17.393429] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv\n'}
[17.393542] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.sh\n'}
[17.393744] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv\n'}
[17.393799] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.bash\n'}
[17.393836] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.sh\n'}
[17.393879] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh\n'}
[17.393916] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv\n'}
[17.394013] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.dsv\n'}
[17.394053] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user\n'}
[17.394096] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[17.394133] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake\n'}
[17.394169] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake\n'}
[17.394205] (xt_user) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.xml\n'}
[17.395713] (xt_user) CommandEnded: {'returncode': 0}
[17.399870] (-) TimerEvent: {}
[17.425879] (xt_user) JobEnded: {'identifier': 'xt_user', 'rc': 0}
[17.427242] (-) EventReactorShutdown: {}
