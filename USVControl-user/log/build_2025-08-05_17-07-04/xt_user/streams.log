[0.011s] Invoking command in '/home/<USER>/project2_new/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new/USVControl-user/build/xt_user -- -j8 -l8
[0.065s] [ 14%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
[0.385s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In constructor ‘[01m[KMotionPlan::MotionPlan()[m[K’:
[0.385s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:35:15:[m[K [01;35m[Kwarning: [m[K‘[01m[Kvoid* memset(void*, int, size_t)[m[K’ clearing an object of type ‘[01m[Kstruct ObstacleAvoidanceParam[m[K’ with no trivial copy-assignment; use assignment or value-initialization instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wclass-memaccess-Wclass-memaccess]8;;[m[K]
[0.385s]    35 |         [01;35m[Kmemset(&obstacleAvoidance, 0, sizeof(ObstacleAvoidanceParam))[m[K;
[0.385s]       |         [01;35m[K~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[0.385s] In file included from [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[0.385s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:48:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstruct ObstacleAvoidanceParam[m[K’ declared here
[0.385s]    48 | [01;36m[K{[m[K
[0.385s]       | [01;36m[K^[m[K
[0.389s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[0.389s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:260:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kboatstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[0.389s]   260 | void MotionPlan::TrajecotryGet([01;35m[KTransState boatstate[m[K, anchor_mission_control task)
[0.389s]       |                                [01;35m[K~~~~~~~~~~~^~~~~~~~~[m[K
[0.389s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)[m[K’:
[0.389s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:293:47:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[0][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
[0.389s]   293 |         float pos[2] = { [01;35m[Kboatstate.PostoHome[0][m[K, boatstate.PostoHome[1] };
[0.389s]       |                          [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[0.389s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:293:71:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[1][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
[0.389s]   293 |         float pos[2] = { boatstate.PostoHome[0], [01;35m[Kboatstate.PostoHome[1][m[K };
[0.389s]       |                                                  [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[0.390s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:368:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kunsigned int[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[0.390s]   368 |     if ([01;35m[KdotTrackingParam.TrackNum > phaseTransitionFlag[m[K) {
[0.390s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~[m[K
[0.396s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kfloat MotionPlan::CalculateAvoidanceAngle(float*, float*, float)[m[K’:
[0.397s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:478:49:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KcurrentPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[0.397s]   478 | float MotionPlan::CalculateAvoidanceAngle([01;35m[Kfloat currentPos[2][m[K, float targetPos[2], float originalAngle)
[0.397s]       |                                           [01;35m[K~~~~~~^~~~~~~~~~~~~[m[K
[0.397s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:478:70:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtargetPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[0.397s]   478 | float MotionPlan::CalculateAvoidanceAngle(float currentPos[2], [01;35m[Kfloat targetPos[2][m[K, float originalAngle)
[0.397s]       |                                                                [01;35m[K~~~~~~^~~~~~~~~~~~[m[K
[0.397s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kbool MotionPlan::CheckMissionFailure(float*)[m[K’:
[0.397s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:510:44:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KcurrentPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[0.397s]   510 | bool MotionPlan::CheckMissionFailure([01;35m[Kfloat currentPos[2][m[K)
[0.397s]       |                                      [01;35m[K~~~~~~^~~~~~~~~~~~~[m[K
[0.587s] [ 28%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[0.717s] [ 71%] Built target planning_hpp
[0.748s] [ 85%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[4.989s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[4.989s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:260:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[4.989s]   260 |     double [01;35m[Kstime[m[K, etime;
[4.989s]       |            [01;35m[K^~~~~[m[K
[4.989s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:260:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[4.989s]   260 |     double stime, [01;35m[Ketime[m[K;
[4.989s]       |                   [01;35m[K^~~~~[m[K
[4.989s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[4.990s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[4.990s]   304 |     double [01;35m[Kstart[m[K,end;
[4.990s]       |            [01;35m[K^~~~~[m[K
[4.990s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:304:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.990s]   304 |     double start,[01;35m[Kend[m[K;
[4.990s]       |                  [01;35m[K^~~[m[K
[4.990s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:305:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.990s]   305 |     int [01;35m[Kdatanum[m[K = 0;
[4.990s]       |         [01;35m[K^~~~~~~[m[K
[4.990s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:307:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[4.990s]   307 |     unsigned char [01;35m[Kperiod[m[K = 0;
[4.990s]       |                   [01;35m[K^~~~~~[m[K
[4.990s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:302:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[4.990s]   302 | void* MapShow([01;35m[Kvoid *arg[m[K)
[4.991s]       |               [01;35m[K~~~~~~^~~[m[K
[5.011s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[5.012s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:357:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.012s]   357 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
[5.012s]       |                 [01;35m[K^[m[K
[5.019s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:358:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.019s]   358 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
[5.019s]       |                 [01;35m[K^[m[K
[5.026s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:359:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.026s]   359 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
[5.026s]       |                 [01;35m[K^[m[K
[5.032s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:360:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.032s]   360 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
[5.033s]       |                 [01;35m[K^[m[K
[5.033s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:361:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.033s]   361 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
[5.033s]       |                 [01;35m[K^[m[K
[5.033s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:362:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.033s]   362 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
[5.033s]       |                 [01;35m[K^[m[K
[5.033s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:363:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.033s]   363 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
[5.033s]       |                 [01;35m[K^[m[K
[5.034s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:364:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.034s]   364 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
[5.034s]       |                 [01;35m[K^[m[K
[5.034s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:365:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.034s]   365 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
[5.034s]       |                 [01;35m[K^[m[K
[5.034s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:366:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
[5.034s]   366 |             [01;35m[K}[m[K;
[5.034s]       |             [01;35m[K^[m[K
[5.036s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:379:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[5.036s]   379 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
[5.036s]       |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[5.144s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:703:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
[5.144s]   703 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
[5.144s]       |                                                            [01;35m[K^~~[m[K
[5.151s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:383:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.151s]   383 |     double [01;35m[Kstart[m[K, end;
[5.151s]       |            [01;35m[K^~~~~[m[K
[5.151s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:383:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.151s]   383 |     double start, [01;35m[Kend[m[K;
[5.151s]       |                   [01;35m[K^~~[m[K
[5.151s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:384:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.151s]   384 |     double [01;35m[Kduration[m[K[10];
[5.151s]       |            [01;35m[K^~~~~~~~[m[K
[5.151s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:385:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.152s]   385 |     int [01;35m[Kdatanum[m[K = 0;
[5.152s]       |         [01;35m[K^~~~~~~[m[K
[5.152s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:427:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.152s]   427 |     double [01;35m[Kanglevel[m[K[2];
[5.152s]       |            [01;35m[K^~~~~~~~[m[K
[16.257s] [100%] [32m[1mLinking CXX executable xt_user_node[0m
[17.350s] [100%] Built target xt_user_node
[17.365s] Invoked command in '/home/<USER>/project2_new/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new/USVControl-user/build/xt_user -- -j8 -l8
[17.380s] Invoking command in '/home/<USER>/project2_new/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/project2_new/USVControl-user/build/xt_user
[17.386s] -- Install configuration: ""
[17.387s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so
[17.387s] -- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so" to ""
[17.387s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
[17.392s] -- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node" to ""
[17.392s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
[17.392s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.sh
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
[17.393s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.dsv
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
[17.393s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.xml
[17.395s] Invoked command in '/home/<USER>/project2_new/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/project2_new/USVControl-user/build/xt_user
