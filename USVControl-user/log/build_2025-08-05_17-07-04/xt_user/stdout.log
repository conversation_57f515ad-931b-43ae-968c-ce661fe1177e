[ 14%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
[ 28%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[ 71%] Built target planning_hpp
[ 85%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[100%] [32m[1mLinking CXX executable xt_user_node[0m
[100%] Built target xt_user_node
-- Install configuration: ""
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so
-- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so" to ""
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
-- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node" to ""
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.sh
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.dsv
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
-- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.xml
