[0.013s] Invoking command in '/home/<USER>/project2_new/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new/USVControl-user/build/xt_user -- -j8 -l8
[0.069s] [ 71%] Built target planning_hpp
[0.094s] [100%] Built target xt_user_node
[0.106s] Invoked command in '/home/<USER>/project2_new/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new/USVControl-user/build/xt_user -- -j8 -l8
[0.117s] Invoking command in '/home/<USER>/project2_new/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/project2_new/USVControl-user/build/xt_user
[0.127s] -- Install configuration: ""
[0.130s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so
[0.130s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
[0.130s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
[0.130s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
[0.131s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh
[0.131s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
[0.132s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.sh
[0.132s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
[0.132s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
[0.132s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
[0.132s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
[0.132s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
[0.132s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.dsv
[0.132s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
[0.132s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
[0.134s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
[0.134s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
[0.134s] -- Up-to-date: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.xml
[0.134s] Invoked command in '/home/<USER>/project2_new/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/project2_new/USVControl-user/build/xt_user
