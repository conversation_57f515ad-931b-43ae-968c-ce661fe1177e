[0.000000] (-) TimerEvent: {}
[0.001339] (xt_user) JobQueued: {'identifier': 'xt_user', 'dependencies': OrderedDict()}
[0.001383] (xt_user) JobStarted: {'identifier': 'xt_user'}
[0.008643] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'cmake'}
[0.009150] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/project2_new/USVControl-user/ControlNode/planning', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/project2_new/USVControl-user/install/xt_user'], 'cwd': '/home/<USER>/project2_new/USVControl-user/build/xt_user', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/project2_new/USVControl-user'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1641'), ('SYSTEMD_EXEC_PID', '1819'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '5901'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:26754'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1784,unix/xumj-virtual-machine:/tmp/.ICE-unix/1784'), ('INVOCATION_ID', '01346c58a1014ccf92e5353cff7841ee'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.RFXTA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-6d8174bbac.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/project2_new/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.089994] (xt_user) StdoutLine: {'line': b'-- The C compiler identification is GNU 11.4.0\n'}
[0.100968] (-) TimerEvent: {}
[0.143404] (xt_user) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 11.4.0\n'}
[0.153171] (xt_user) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.202000] (-) TimerEvent: {}
[0.229407] (xt_user) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.236625] (xt_user) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc - skipped\n'}
[0.237096] (xt_user) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.237282] (xt_user) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.239941] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.303029] (-) TimerEvent: {}
[0.324191] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[0.329861] (xt_user) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ - skipped\n'}
[0.330235] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[0.330572] (xt_user) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[0.343289] (xt_user) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.403113] (-) TimerEvent: {}
[0.502745] (xt_user) StdoutLine: {'line': b'-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter \n'}
[0.503225] (-) TimerEvent: {}
[0.604297] (-) TimerEvent: {}
[0.611733] (xt_user) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.668244] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.673274] (xt_user) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.682311] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.698096] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.704459] (-) TimerEvent: {}
[0.715242] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.782354] (xt_user) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.785403] (xt_user) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.804691] (-) TimerEvent: {}
[0.883559] (xt_user) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  \n'}
[0.905312] (-) TimerEvent: {}
[0.906912] (xt_user) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/humble/include  \n'}
[0.944697] (xt_user) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.956892] (xt_user) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[1.005425] (-) TimerEvent: {}
[1.024537] (xt_user) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[1.025023] (xt_user) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[1.105542] (xt_user) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success\n'}
[1.105916] (-) TimerEvent: {}
[1.107120] (xt_user) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[1.177654] (xt_user) StdoutLine: {'line': b'-- Found OpenCV: /usr (found version "4.5.4") \n'}
[1.179487] (xt_user) StdoutLine: {'line': b'-- Found nlohmann_json: /usr/local/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.12.0") \n'}
[1.181679] (xt_user) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[1.206358] (-) TimerEvent: {}
[1.241705] (xt_user) StdoutLine: {'line': b'-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system \n'}
[1.306636] (-) TimerEvent: {}
[1.309123] (xt_user) StdoutLine: {'line': b'-- Configuring done\n'}
[1.329000] (xt_user) StdoutLine: {'line': b'-- Generating done\n'}
[1.336476] (xt_user) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/project2_new/USVControl-user/build/xt_user\n'}
[1.346822] (xt_user) CommandEnded: {'returncode': 0}
[1.348045] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'build'}
[1.349805] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/project2_new/USVControl-user/build/xt_user', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/project2_new/USVControl-user/build/xt_user', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/project2_new/USVControl-user'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1641'), ('SYSTEMD_EXEC_PID', '1819'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '5901'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:26754'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1784,unix/xumj-virtual-machine:/tmp/.ICE-unix/1784'), ('INVOCATION_ID', '01346c58a1014ccf92e5353cff7841ee'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.RFXTA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-6d8174bbac.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/project2_new/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[1.400126] (xt_user) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o\x1b[0m\n'}
[1.400583] (xt_user) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o\x1b[0m\n'}
[1.400746] (xt_user) StdoutLine: {'line': b'[ 42%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o\x1b[0m\n'}
[1.400893] (xt_user) StdoutLine: {'line': b'[ 57%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/home/<USER>/project2_new/USVControl-user/ControlNode/include/boat_datadef.cpp.o\x1b[0m\n'}
[1.407703] (-) TimerEvent: {}
[1.508224] (-) TimerEvent: {}
[1.608973] (-) TimerEvent: {}
[1.709476] (-) TimerEvent: {}
[1.795550] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.795750] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:250:43:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kboatstate\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.795804] (xt_user) StderrLine: {'line': b'  250 | void MotionPlan::TrajecotryGet(\x1b[01;35m\x1b[KTransState boatstate\x1b[m\x1b[K, anchor_mission_control task)\n'}
[1.795846] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;35m\x1b[K~~~~~~~~~~~^~~~~~~~~\x1b[m\x1b[K\n'}
[1.795883] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.796005] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:283:47:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[0]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.796067] (xt_user) StderrLine: {'line': b'  283 |         float pos[2] = { \x1b[01;35m\x1b[Kboatstate.PostoHome[0]\x1b[m\x1b[K, boatstate.PostoHome[1] };\n'}
[1.796106] (xt_user) StderrLine: {'line': b'      |                          \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[1.796292] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:283:71:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[1]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.796356] (xt_user) StderrLine: {'line': b'  283 |         float pos[2] = { boatstate.PostoHome[0], \x1b[01;35m\x1b[Kboatstate.PostoHome[1]\x1b[m\x1b[K };\n'}
[1.796397] (xt_user) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[1.796433] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:338:35:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kunsigned int\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.796484] (xt_user) StderrLine: {'line': b'  338 |     if (\x1b[01;35m\x1b[KdotTrackingParam.TrackNum > phaseTransitionFlag\x1b[m\x1b[K) {\n'}
[1.796555] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.810661] (-) TimerEvent: {}
[1.911427] (-) TimerEvent: {}
[1.976038] (xt_user) StdoutLine: {'line': b'[ 71%] \x1b[32m\x1b[1mLinking CXX shared library libplanning_hpp.so\x1b[0m\n'}
[2.011850] (-) TimerEvent: {}
[2.113385] (-) TimerEvent: {}
[2.127366] (xt_user) StdoutLine: {'line': b'[ 71%] Built target planning_hpp\n'}
[2.148080] (xt_user) StdoutLine: {'line': b'[ 85%] \x1b[32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o\x1b[0m\n'}
[2.214835] (-) TimerEvent: {}
[2.315691] (-) TimerEvent: {}
[2.418036] (-) TimerEvent: {}
[2.526033] (-) TimerEvent: {}
[2.626814] (-) TimerEvent: {}
[2.728779] (-) TimerEvent: {}
[2.829633] (-) TimerEvent: {}
[2.930517] (-) TimerEvent: {}
[3.031029] (-) TimerEvent: {}
[3.131692] (-) TimerEvent: {}
[3.232866] (-) TimerEvent: {}
[3.333355] (-) TimerEvent: {}
[3.434808] (-) TimerEvent: {}
[3.535492] (-) TimerEvent: {}
[3.637246] (-) TimerEvent: {}
[3.737833] (-) TimerEvent: {}
[3.838272] (-) TimerEvent: {}
[3.939442] (-) TimerEvent: {}
[4.041396] (-) TimerEvent: {}
[4.142931] (-) TimerEvent: {}
[4.244358] (-) TimerEvent: {}
[4.344911] (-) TimerEvent: {}
[4.445576] (-) TimerEvent: {}
[4.546356] (-) TimerEvent: {}
[4.647688] (-) TimerEvent: {}
[4.748642] (-) TimerEvent: {}
[4.850404] (-) TimerEvent: {}
[4.950938] (-) TimerEvent: {}
[5.051828] (-) TimerEvent: {}
[5.152983] (-) TimerEvent: {}
[5.253620] (-) TimerEvent: {}
[5.354071] (-) TimerEvent: {}
[5.455975] (-) TimerEvent: {}
[5.556487] (-) TimerEvent: {}
[5.657400] (-) TimerEvent: {}
[5.758709] (-) TimerEvent: {}
[5.859557] (-) TimerEvent: {}
[5.960508] (-) TimerEvent: {}
[6.062011] (-) TimerEvent: {}
[6.163458] (-) TimerEvent: {}
[6.251694] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[6.251900] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:212:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kpreposture\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.251966] (xt_user) StderrLine: {'line': b'  212 |     kinematic_state \x1b[01;35m\x1b[Kpreposture\x1b[m\x1b[K;\n'}
[6.252002] (xt_user) StderrLine: {'line': b'      |                     \x1b[01;35m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[6.255989] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[6.256202] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:239:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.256298] (xt_user) StderrLine: {'line': b'  239 |     double \x1b[01;35m\x1b[Kstime\x1b[m\x1b[K, etime;\n'}
[6.256532] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[6.256600] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:239:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Ketime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.256659] (xt_user) StderrLine: {'line': b'  239 |     double stime, \x1b[01;35m\x1b[Ketime\x1b[m\x1b[K;\n'}
[6.256696] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[6.256732] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid* MapShow(void*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[6.256771] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:283:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.256802] (xt_user) StderrLine: {'line': b'  283 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K,end;\n'}
[6.256833] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[6.256863] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:283:18:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.256897] (xt_user) StderrLine: {'line': b'  283 |     double start,\x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[6.256927] (xt_user) StderrLine: {'line': b'      |                  \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[6.256957] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:284:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.257003] (xt_user) StderrLine: {'line': b'  284 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[6.257036] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[6.257152] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:286:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kperiod\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.257193] (xt_user) StderrLine: {'line': b'  286 |     unsigned char \x1b[01;35m\x1b[Kperiod\x1b[m\x1b[K = 0;\n'}
[6.257228] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[6.257268] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:281:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Karg\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.257303] (xt_user) StderrLine: {'line': b'  281 | void* MapShow(\x1b[01;35m\x1b[Kvoid *arg\x1b[m\x1b[K)\n'}
[6.257337] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K~~~~~~^~~\x1b[m\x1b[K\n'}
[6.263851] (-) TimerEvent: {}
[6.272158] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[6.272417] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:336:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.272485] (xt_user) StderrLine: {'line': b'  336 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kcommand = wp["command"].get<uint16_t>(),\n'}
[6.272541] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.282508] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:337:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.282794] (xt_user) StderrLine: {'line': b'  337 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kseq = wp["seq"].get<uint8_t>(),\n'}
[6.282878] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.289964] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:338:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.290142] (xt_user) StderrLine: {'line': b'  338 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam1 = wp["param1"].get<float>(),\n'}
[6.290184] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.295770] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:339:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.295997] (xt_user) StderrLine: {'line': b'  339 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam2 = wp["param2"].get<float>(),\n'}
[6.296039] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.296133] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:340:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.296190] (xt_user) StderrLine: {'line': b'  340 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam3 = wp["param3"].get<float>(),\n'}
[6.296225] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.296268] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:341:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.296323] (xt_user) StderrLine: {'line': b'  341 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam4 = wp["param4"].get<float>(),\n'}
[6.296508] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.296568] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:342:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.296605] (xt_user) StderrLine: {'line': b'  342 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klat = wp["x"].get<float>(),\n'}
[6.296636] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.297302] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:343:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.297397] (xt_user) StderrLine: {'line': b'  343 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klon = wp["y"].get<float>(),\n'}
[6.297576] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.297630] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:344:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.297744] (xt_user) StderrLine: {'line': b'  344 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kalt = wp["z"].get<float>()\n'}
[6.297791] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.297822] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:345:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kmissing initializer for member \xe2\x80\x98\x1b[01m\x1b[KMissionItem::reserve\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers\x07-Wmissing-field-initializers\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.297854] (xt_user) StderrLine: {'line': b'  345 |             \x1b[01;35m\x1b[K}\x1b[m\x1b[K;\n'}
[6.297883] (xt_user) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[6.298830] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:358:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<MissionItem>::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.298915] (xt_user) StderrLine: {'line': b'  358 |     for(int i = 0; \x1b[01;35m\x1b[Ki<missions.size()\x1b[m\x1b[K; i++){\n'}
[6.298955] (xt_user) StderrLine: {'line': b'      |                    \x1b[01;35m\x1b[K~^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[6.364892] (-) TimerEvent: {}
[6.399175] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:682:60:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunsigned conversion from \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Ku_int8_t\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kunsigned char\x1b[m\x1b[K\xe2\x80\x99} changes value from \xe2\x80\x98\x1b[01m\x1b[K512\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[K0\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow\x07-Woverflow\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.399384] (xt_user) StderrLine: {'line': b'  682 |                     taskAnchorMissionControl.mission_num = \x1b[01;35m\x1b[K512\x1b[m\x1b[K;\n'}
[6.399430] (xt_user) StderrLine: {'line': b'      |                                                            \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[6.406806] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:362:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.407135] (xt_user) StderrLine: {'line': b'  362 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K, end;\n'}
[6.407199] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[6.407255] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:362:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.407315] (xt_user) StderrLine: {'line': b'  362 |     double start, \x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[6.407370] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[6.407424] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:363:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kduration\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.407488] (xt_user) StderrLine: {'line': b'  363 |     double \x1b[01;35m\x1b[Kduration\x1b[m\x1b[K[10];\n'}
[6.407541] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[6.407581] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:364:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.407612] (xt_user) StderrLine: {'line': b'  364 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[6.407642] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[6.407671] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:406:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kanglevel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[6.407702] (xt_user) StderrLine: {'line': b'  406 |     double \x1b[01;35m\x1b[Kanglevel\x1b[m\x1b[K[2];\n'}
[6.407731] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[6.465460] (-) TimerEvent: {}
[6.566744] (-) TimerEvent: {}
[6.669976] (-) TimerEvent: {}
[6.770529] (-) TimerEvent: {}
[6.871830] (-) TimerEvent: {}
[6.973825] (-) TimerEvent: {}
[7.075077] (-) TimerEvent: {}
[7.176992] (-) TimerEvent: {}
[7.277629] (-) TimerEvent: {}
[7.378221] (-) TimerEvent: {}
[7.480209] (-) TimerEvent: {}
[7.580601] (-) TimerEvent: {}
[7.682024] (-) TimerEvent: {}
[7.782476] (-) TimerEvent: {}
[7.884236] (-) TimerEvent: {}
[7.986347] (-) TimerEvent: {}
[8.087549] (-) TimerEvent: {}
[8.189356] (-) TimerEvent: {}
[8.289972] (-) TimerEvent: {}
[8.391856] (-) TimerEvent: {}
[8.493806] (-) TimerEvent: {}
[8.595631] (-) TimerEvent: {}
[8.697341] (-) TimerEvent: {}
[8.798130] (-) TimerEvent: {}
[8.898625] (-) TimerEvent: {}
[9.000085] (-) TimerEvent: {}
[9.101359] (-) TimerEvent: {}
[9.202332] (-) TimerEvent: {}
[9.302753] (-) TimerEvent: {}
[9.403404] (-) TimerEvent: {}
[9.504346] (-) TimerEvent: {}
[9.605602] (-) TimerEvent: {}
[9.706128] (-) TimerEvent: {}
[9.806679] (-) TimerEvent: {}
[9.908493] (-) TimerEvent: {}
[10.008964] (-) TimerEvent: {}
[10.110404] (-) TimerEvent: {}
[10.211579] (-) TimerEvent: {}
[10.312318] (-) TimerEvent: {}
[10.413233] (-) TimerEvent: {}
[10.514402] (-) TimerEvent: {}
[10.615700] (-) TimerEvent: {}
[10.716669] (-) TimerEvent: {}
[10.818547] (-) TimerEvent: {}
[10.919512] (-) TimerEvent: {}
[11.021188] (-) TimerEvent: {}
[11.123061] (-) TimerEvent: {}
[11.223855] (-) TimerEvent: {}
[11.325877] (-) TimerEvent: {}
[11.427151] (-) TimerEvent: {}
[11.528095] (-) TimerEvent: {}
[11.629045] (-) TimerEvent: {}
[11.731045] (-) TimerEvent: {}
[11.832517] (-) TimerEvent: {}
[11.933977] (-) TimerEvent: {}
[12.035080] (-) TimerEvent: {}
[12.135816] (-) TimerEvent: {}
[12.237000] (-) TimerEvent: {}
[12.338607] (-) TimerEvent: {}
[12.440670] (-) TimerEvent: {}
[12.541923] (-) TimerEvent: {}
[12.643339] (-) TimerEvent: {}
[12.743826] (-) TimerEvent: {}
[12.844755] (-) TimerEvent: {}
[12.945739] (-) TimerEvent: {}
[13.046510] (-) TimerEvent: {}
[13.147350] (-) TimerEvent: {}
[13.247985] (-) TimerEvent: {}
[13.349457] (-) TimerEvent: {}
[13.450598] (-) TimerEvent: {}
[13.551830] (-) TimerEvent: {}
[13.653447] (-) TimerEvent: {}
[13.754113] (-) TimerEvent: {}
[13.855529] (-) TimerEvent: {}
[13.957545] (-) TimerEvent: {}
[14.058732] (-) TimerEvent: {}
[14.159843] (-) TimerEvent: {}
[14.260842] (-) TimerEvent: {}
[14.361796] (-) TimerEvent: {}
[14.462284] (-) TimerEvent: {}
[14.563229] (-) TimerEvent: {}
[14.665040] (-) TimerEvent: {}
[14.766297] (-) TimerEvent: {}
[14.868052] (-) TimerEvent: {}
[14.969782] (-) TimerEvent: {}
[15.070268] (-) TimerEvent: {}
[15.171204] (-) TimerEvent: {}
[15.272945] (-) TimerEvent: {}
[15.373727] (-) TimerEvent: {}
[15.474933] (-) TimerEvent: {}
[15.575900] (-) TimerEvent: {}
[15.677863] (-) TimerEvent: {}
[15.778736] (-) TimerEvent: {}
[15.879242] (-) TimerEvent: {}
[15.979724] (-) TimerEvent: {}
[16.080485] (-) TimerEvent: {}
[16.181409] (-) TimerEvent: {}
[16.283080] (-) TimerEvent: {}
[16.384986] (-) TimerEvent: {}
[16.486861] (-) TimerEvent: {}
[16.587343] (-) TimerEvent: {}
[16.687889] (-) TimerEvent: {}
[16.698929] (xt_user) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable xt_user_node\x1b[0m\n'}
[16.788559] (-) TimerEvent: {}
[16.889362] (-) TimerEvent: {}
[16.990395] (-) TimerEvent: {}
[17.092142] (-) TimerEvent: {}
[17.193151] (-) TimerEvent: {}
[17.294624] (-) TimerEvent: {}
[17.396603] (-) TimerEvent: {}
[17.497259] (-) TimerEvent: {}
[17.598652] (-) TimerEvent: {}
[17.699207] (-) TimerEvent: {}
[17.800185] (-) TimerEvent: {}
[17.804373] (xt_user) StdoutLine: {'line': b'[100%] Built target xt_user_node\n'}
[17.818559] (xt_user) CommandEnded: {'returncode': 0}
[17.819832] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'install'}
[17.829059] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/project2_new/USVControl-user/build/xt_user'], 'cwd': '/home/<USER>/project2_new/USVControl-user/build/xt_user', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/project2_new/USVControl-user'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1641'), ('SYSTEMD_EXEC_PID', '1819'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '5901'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:26754'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1784,unix/xumj-virtual-machine:/tmp/.ICE-unix/1784'), ('INVOCATION_ID', '01346c58a1014ccf92e5353cff7841ee'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.RFXTA3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-6d8174bbac.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/project2_new/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[17.835187] (xt_user) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[17.835449] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so\n'}
[17.835512] (xt_user) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so" to ""\n'}
[17.835550] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node\n'}
[17.840599] (xt_user) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node" to ""\n'}
[17.840998] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user\n'}
[17.841283] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user\n'}
[17.841389] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh\n'}
[17.841549] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv\n'}
[17.841632] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.sh\n'}
[17.841740] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv\n'}
[17.841811] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.bash\n'}
[17.841932] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.sh\n'}
[17.842334] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh\n'}
[17.842497] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv\n'}
[17.842708] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.dsv\n'}
[17.842948] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user\n'}
[17.843169] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake\n'}
[17.843312] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake\n'}
[17.843456] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake\n'}
[17.843498] (xt_user) StdoutLine: {'line': b'-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.xml\n'}
[17.845728] (xt_user) CommandEnded: {'returncode': 0}
[17.865730] (xt_user) JobEnded: {'identifier': 'xt_user', 'rc': 0}
[17.866883] (-) EventReactorShutdown: {}
