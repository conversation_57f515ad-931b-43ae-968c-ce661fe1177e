-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler AB<PERSON> info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
-- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
-- Found FastRTPS: /opt/ros/humble/include  
-- Using RMW implementation 'rmw_fastrtps_cpp' as default
-- Looking for pthread.h
-- Looking for pthread.h - found
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE  
-- Found OpenCV: /usr (found version "4.5.4") 
-- Found nlohmann_json: /usr/local/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.12.0") 
-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
-- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system 
-- Configuring done
-- Generating done
-- Build files have been written to: /home/<USER>/project2_new/USVControl-user/build/xt_user
[ 28%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
[ 28%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[ 42%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o[0m
[ 57%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/home/<USER>/project2_new/USVControl-user/ControlNode/include/boat_datadef.cpp.o[0m
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:250:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kboatstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  250 | void MotionPlan::TrajecotryGet([01;35m[KTransState boatstate[m[K, anchor_mission_control task)
      |                                [01;35m[K~~~~~~~~~~~^~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:283:47:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[0][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
  283 |         float pos[2] = { [01;35m[Kboatstate.PostoHome[0][m[K, boatstate.PostoHome[1] };
      |                          [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:283:71:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[1][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
  283 |         float pos[2] = { boatstate.PostoHome[0], [01;35m[Kboatstate.PostoHome[1][m[K };
      |                                                  [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:338:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kunsigned int[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
  338 |     if ([01;35m[KdotTrackingParam.TrackNum > phaseTransitionFlag[m[K) {
      |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~[m[K
[ 71%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[ 71%] Built target planning_hpp
[ 85%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:212:21:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpreposture[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  212 |     kinematic_state [01;35m[Kpreposture[m[K;
      |                     [01;35m[K^~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:239:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  239 |     double [01;35m[Kstime[m[K, etime;
      |            [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:239:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  239 |     double stime, [01;35m[Ketime[m[K;
      |                   [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:283:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  283 |     double [01;35m[Kstart[m[K,end;
      |            [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:283:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  283 |     double start,[01;35m[Kend[m[K;
      |                  [01;35m[K^~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:284:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  284 |     int [01;35m[Kdatanum[m[K = 0;
      |         [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:286:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  286 |     unsigned char [01;35m[Kperiod[m[K = 0;
      |                   [01;35m[K^~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:281:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  281 | void* MapShow([01;35m[Kvoid *arg[m[K)
      |               [01;35m[K~~~~~~^~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:336:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  336 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:337:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  337 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:338:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  338 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:339:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  339 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:340:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  340 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:341:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  341 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:342:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  342 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:343:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  343 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:344:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
  344 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
      |                 [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:345:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
  345 |             [01;35m[K}[m[K;
      |             [01;35m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:358:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
  358 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
      |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:682:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
  682 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
      |                                                            [01;35m[K^~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:362:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
  362 |     double [01;35m[Kstart[m[K, end;
      |            [01;35m[K^~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:362:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  362 |     double start, [01;35m[Kend[m[K;
      |                   [01;35m[K^~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:363:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  363 |     double [01;35m[Kduration[m[K[10];
      |            [01;35m[K^~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:364:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  364 |     int [01;35m[Kdatanum[m[K = 0;
      |         [01;35m[K^~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:406:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  406 |     double [01;35m[Kanglevel[m[K[2];
      |            [01;35m[K^~~~~~~~[m[K
[100%] [32m[1mLinking CXX executable xt_user_node[0m
[100%] Built target xt_user_node
-- Install configuration: ""
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so
-- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so" to ""
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
-- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node" to ""
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.sh
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.dsv
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
-- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.xml
