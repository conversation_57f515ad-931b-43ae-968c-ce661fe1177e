[0.009s] Invoking command in '/home/<USER>/project2_new/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/project2_new/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/project2_new/USVControl-user/install/xt_user
[0.089s] -- The C compiler identification is GNU 11.4.0
[0.142s] -- The CXX compiler identification is GNU 11.4.0
[0.152s] -- Detecting C compiler ABI info
[0.228s] -- Detecting C compiler ABI info - done
[0.235s] -- Check for working C compiler: /usr/bin/cc - skipped
[0.236s] -- Detecting C compile features
[0.236s] -- Detecting C compile features - done
[0.239s] -- Detecting CXX compiler ABI info
[0.323s] -- Detecting CXX compiler AB<PERSON> info - done
[0.329s] -- Check for working CXX compiler: /usr/bin/c++ - skipped
[0.329s] -- Detecting CXX compile features
[0.329s] -- Detecting CXX compile features - done
[0.342s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.502s] -- Found Python3: /usr/bin/python3 (found version "3.10.12") found components: Interpreter 
[0.610s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.667s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.672s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.681s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.697s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.714s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.781s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.784s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.882s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "3.0.2")  
[0.906s] -- Found FastRTPS: /opt/ros/humble/include  
[0.944s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.956s] -- Looking for pthread.h
[1.023s] -- Looking for pthread.h - found
[1.024s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[1.104s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
[1.106s] -- Found Threads: TRUE  
[1.176s] -- Found OpenCV: /usr (found version "4.5.4") 
[1.178s] -- Found nlohmann_json: /usr/local/share/cmake/nlohmann_json/nlohmann_jsonConfig.cmake (found version "3.12.0") 
[1.180s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[1.240s] -- Found Boost: /usr/lib/x86_64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system 
[1.308s] -- Configuring done
[1.328s] -- Generating done
[1.335s] -- Build files have been written to: /home/<USER>/project2_new/USVControl-user/build/xt_user
[1.346s] Invoked command in '/home/<USER>/project2_new/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake /home/<USER>/project2_new/USVControl-user/ControlNode/planning -DCMAKE_INSTALL_PREFIX=/home/<USER>/project2_new/USVControl-user/install/xt_user
[1.349s] Invoking command in '/home/<USER>/project2_new/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new/USVControl-user/build/xt_user -- -j8 -l8
[1.399s] [ 28%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
[1.399s] [ 28%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[1.399s] [ 42%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/pid_control.cpp.o[0m
[1.400s] [ 57%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/home/<USER>/project2_new/USVControl-user/ControlNode/include/boat_datadef.cpp.o[0m
[1.794s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[1.794s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:250:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kboatstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[1.794s]   250 | void MotionPlan::TrajecotryGet([01;35m[KTransState boatstate[m[K, anchor_mission_control task)
[1.794s]       |                                [01;35m[K~~~~~~~~~~~^~~~~~~~~[m[K
[1.795s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)[m[K’:
[1.795s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:283:47:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[0][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
[1.795s]   283 |         float pos[2] = { [01;35m[Kboatstate.PostoHome[0][m[K, boatstate.PostoHome[1] };
[1.795s]       |                          [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[1.795s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:283:71:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[1][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
[1.795s]   283 |         float pos[2] = { boatstate.PostoHome[0], [01;35m[Kboatstate.PostoHome[1][m[K };
[1.795s]       |                                                  [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[1.795s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:338:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kunsigned int[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[1.795s]   338 |     if ([01;35m[KdotTrackingParam.TrackNum > phaseTransitionFlag[m[K) {
[1.795s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~[m[K
[1.975s] [ 71%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[2.126s] [ 71%] Built target planning_hpp
[2.147s] [ 85%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[6.250s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid NeiborStateCallback(std_msgs::msg::Float64MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[6.251s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:212:21:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kpreposture[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[6.251s]   212 |     kinematic_state [01;35m[Kpreposture[m[K;
[6.251s]       |                     [01;35m[K^~~~~~~~~~[m[K
[6.255s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[6.255s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:239:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[6.255s]   239 |     double [01;35m[Kstime[m[K, etime;
[6.255s]       |            [01;35m[K^~~~~[m[K
[6.255s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:239:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[6.255s]   239 |     double stime, [01;35m[Ketime[m[K;
[6.255s]       |                   [01;35m[K^~~~~[m[K
[6.255s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[6.255s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:283:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[6.255s]   283 |     double [01;35m[Kstart[m[K,end;
[6.255s]       |            [01;35m[K^~~~~[m[K
[6.256s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:283:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.256s]   283 |     double start,[01;35m[Kend[m[K;
[6.256s]       |                  [01;35m[K^~~[m[K
[6.256s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:284:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.256s]   284 |     int [01;35m[Kdatanum[m[K = 0;
[6.256s]       |         [01;35m[K^~~~~~~[m[K
[6.256s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:286:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.256s]   286 |     unsigned char [01;35m[Kperiod[m[K = 0;
[6.256s]       |                   [01;35m[K^~~~~~[m[K
[6.256s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:281:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[6.256s]   281 | void* MapShow([01;35m[Kvoid *arg[m[K)
[6.256s]       |               [01;35m[K~~~~~~^~~[m[K
[6.271s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[6.271s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:336:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[6.271s]   336 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
[6.271s]       |                 [01;35m[K^[m[K
[6.281s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:337:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[6.281s]   337 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
[6.282s]       |                 [01;35m[K^[m[K
[6.289s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:338:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[6.289s]   338 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
[6.289s]       |                 [01;35m[K^[m[K
[6.295s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:339:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[6.295s]   339 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
[6.295s]       |                 [01;35m[K^[m[K
[6.295s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:340:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[6.295s]   340 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
[6.295s]       |                 [01;35m[K^[m[K
[6.295s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:341:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[6.295s]   341 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
[6.295s]       |                 [01;35m[K^[m[K
[6.295s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:342:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[6.295s]   342 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
[6.295s]       |                 [01;35m[K^[m[K
[6.296s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:343:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[6.296s]   343 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
[6.296s]       |                 [01;35m[K^[m[K
[6.296s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:344:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[6.296s]   344 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
[6.296s]       |                 [01;35m[K^[m[K
[6.296s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:345:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
[6.296s]   345 |             [01;35m[K}[m[K;
[6.297s]       |             [01;35m[K^[m[K
[6.298s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:358:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[6.298s]   358 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
[6.298s]       |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[6.398s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:682:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
[6.398s]   682 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
[6.398s]       |                                                            [01;35m[K^~~[m[K
[6.406s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:362:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[6.406s]   362 |     double [01;35m[Kstart[m[K, end;
[6.406s]       |            [01;35m[K^~~~~[m[K
[6.406s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:362:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.406s]   362 |     double start, [01;35m[Kend[m[K;
[6.406s]       |                   [01;35m[K^~~[m[K
[6.406s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:363:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.406s]   363 |     double [01;35m[Kduration[m[K[10];
[6.406s]       |            [01;35m[K^~~~~~~~[m[K
[6.406s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:364:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.406s]   364 |     int [01;35m[Kdatanum[m[K = 0;
[6.406s]       |         [01;35m[K^~~~~~~[m[K
[6.406s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:406:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[6.406s]   406 |     double [01;35m[Kanglevel[m[K[2];
[6.406s]       |            [01;35m[K^~~~~~~~[m[K
[16.698s] [100%] [32m[1mLinking CXX executable xt_user_node[0m
[17.803s] [100%] Built target xt_user_node
[17.817s] Invoked command in '/home/<USER>/project2_new/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new/USVControl-user/build/xt_user -- -j8 -l8
[17.828s] Invoking command in '/home/<USER>/project2_new/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/project2_new/USVControl-user/build/xt_user
[17.834s] -- Install configuration: ""
[17.834s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so
[17.834s] -- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/libplanning_hpp.so" to ""
[17.834s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node
[17.839s] -- Set runtime path of "/home/<USER>/project2_new/USVControl-user/install/xt_user/lib/xt_user/xt_user_node" to ""
[17.840s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/package_run_dependencies/xt_user
[17.840s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/parent_prefix_path/xt_user
[17.840s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.sh
[17.840s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/ament_prefix_path.dsv
[17.840s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.sh
[17.840s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/environment/path.dsv
[17.840s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.bash
[17.841s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.sh
[17.841s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.zsh
[17.841s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/local_setup.dsv
[17.841s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.dsv
[17.842s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/ament_index/resource_index/packages/xt_user
[17.842s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/ament_cmake_export_dependencies-extras.cmake
[17.842s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig.cmake
[17.842s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/cmake/xt_userConfig-version.cmake
[17.842s] -- Installing: /home/<USER>/project2_new/USVControl-user/install/xt_user/share/xt_user/package.xml
[17.845s] Invoked command in '/home/<USER>/project2_new/USVControl-user/build/xt_user' returned '0': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --install /home/<USER>/project2_new/USVControl-user/build/xt_user
