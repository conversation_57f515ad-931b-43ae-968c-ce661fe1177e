[0.012s] Invoking command in '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new（复件）/USVControl-user/build/xt_user -- -j8 -l8
[0.021s] [0mCMake Error: The current CMakeCache.txt directory /home/<USER>/project2_new（复件）/USVControl-user/build/xt_user/CMakeCache.txt is different than the directory /home/<USER>/project2_new/USVControl-user/build/xt_user where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt[0m
[0.049s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.187s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.236s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.240s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.248s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.261s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.276s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.319s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.325s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.433s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.506s] -- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)
[0.628s] -- Configuring done
[0.647s] -- Generating done
[0.655s] -- Build files have been written to: /home/<USER>/project2_new/USVControl-user/build/xt_user
[0.684s] [35m[1mConsolidate compiler generated dependencies of target planning_hpp[0m
[0.697s] [ 28%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o[0m
[0.697s] [ 28%] [32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o[0m
[1.037s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In constructor ‘[01m[KMotionPlan::MotionPlan()[m[K’:
[1.037s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:36:15:[m[K [01;35m[Kwarning: [m[K‘[01m[Kvoid* memset(void*, int, size_t)[m[K’ clearing an object of type ‘[01m[Kstruct ObstacleAvoidanceParam[m[K’ with no trivial copy-assignment; use assignment or value-initialization instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wclass-memaccess-Wclass-memaccess]8;;[m[K]
[1.037s]    36 |         [01;35m[Kmemset(&obstacleAvoidance, 0, sizeof(ObstacleAvoidanceParam))[m[K;
[1.038s]       |         [01;35m[K~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[1.038s] In file included from [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[1.038s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:48:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstruct ObstacleAvoidanceParam[m[K’ declared here
[1.038s]    48 | [01;36m[K{[m[K
[1.038s]       | [01;36m[K^[m[K
[1.040s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[1.041s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:261:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kboatstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[1.041s]   261 | void MotionPlan::TrajecotryGet([01;35m[KTransState boatstate[m[K, anchor_mission_control task)
[1.041s]       |                                [01;35m[K~~~~~~~~~~~^~~~~~~~~[m[K
[1.041s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)[m[K’:
[1.041s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:315:47:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[1][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
[1.041s]   315 |         float pos[2] = { [01;35m[Kboatstate.PostoHome[1][m[K, boatstate.PostoHome[0] }; // [X, Y] = [lon, lat]
[1.042s]       |                          [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[1.043s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:315:71:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[0][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
[1.043s]   315 |         float pos[2] = { boatstate.PostoHome[1], [01;35m[Kboatstate.PostoHome[0][m[K }; // [X, Y] = [lon, lat]
[1.043s]       |                                                  [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[1.043s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:458:35:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kunsigned int[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[1.043s]   458 |     if ([01;35m[KdotTrackingParam.TrackNum > phaseTransitionFlag[m[K) {
[1.043s]       |         [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~[m[K
[1.059s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kfloat MotionPlan::CalculateAvoidanceAngle(float*, float*, float)[m[K’:
[1.059s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:647:49:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KcurrentPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[1.059s]   647 | float MotionPlan::CalculateAvoidanceAngle([01;35m[Kfloat currentPos[2][m[K, float targetPos[2], float originalAngle)
[1.059s]       |                                           [01;35m[K~~~~~~^~~~~~~~~~~~~[m[K
[1.059s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:647:70:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtargetPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[1.059s]   647 | float MotionPlan::CalculateAvoidanceAngle(float currentPos[2], [01;35m[Kfloat targetPos[2][m[K, float originalAngle)
[1.060s]       |                                                                [01;35m[K~~~~~~^~~~~~~~~~~~[m[K
[1.061s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kbool MotionPlan::CheckMissionFailure(float*)[m[K’:
[1.061s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:679:44:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KcurrentPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[1.061s]   679 | bool MotionPlan::CheckMissionFailure([01;35m[Kfloat currentPos[2][m[K)
[1.061s]       |                                      [01;35m[K~~~~~~^~~~~~~~~~~~~[m[K
[1.269s] [ 42%] [32m[1mLinking CXX shared library libplanning_hpp.so[0m
[1.396s] [ 71%] Built target planning_hpp
[1.412s] [35m[1mConsolidate compiler generated dependencies of target xt_user_node[0m
[1.437s] [ 85%] [32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o[0m
[5.531s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)[m[K’:
[5.531s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:263:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.531s]   263 |     double [01;35m[Kstime[m[K, etime;
[5.532s]       |            [01;35m[K^~~~~[m[K
[5.532s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:263:19:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Ketime[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.532s]   263 |     double stime, [01;35m[Ketime[m[K;
[5.532s]       |                   [01;35m[K^~~~~[m[K
[5.532s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kvoid* MapShow(void*)[m[K’:
[5.532s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:363:27:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} and ‘[01m[Kint[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[5.532s]   363 |                     if ([01;35m[Ki < current_target[m[K) {
[5.532s]       |                         [01;35m[K~~^~~~~~~~~~~~~~~~[m[K
[5.532s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:367:34:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Ksize_t[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} and ‘[01m[Kint[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[5.532s]   367 |                     } else if ([01;35m[Ki == current_target[m[K) {
[5.532s]       |                                [01;35m[K~~^~~~~~~~~~~~~~~~~[m[K
[5.533s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:442:55:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[5.533s]   442 |             if ([01;35m[KmotionPlanner.GetCurrentTargetIndex() < missions.size()[m[K) {
[5.533s]       |                 [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~[m[K
[5.534s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:458:55:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[5.534s]   458 |             if ([01;35m[KmotionPlanner.GetCurrentTargetIndex() < missions.size()[m[K) {
[5.534s]       |                 [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~[m[K
[5.536s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:558:55:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[5.536s]   558 |             if ([01;35m[KmotionPlanner.GetCurrentTargetIndex() < missions.size()[m[K) {
[5.536s]       |                 [01;35m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~[m[K
[5.537s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:12:[m[K [01;35m[Kwarning: [m[Kvariable ‘[01m[Kstart[m[K’ set but not used [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable-Wunused-but-set-variable]8;;[m[K]
[5.537s]   331 |     double [01;35m[Kstart[m[K,end;
[5.537s]       |            [01;35m[K^~~~~[m[K
[5.538s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:18:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.538s]   331 |     double start,[01;35m[Kend[m[K;
[5.538s]       |                  [01;35m[K^~~[m[K
[5.538s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:332:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.538s]   332 |     int [01;35m[Kdatanum[m[K = 0;
[5.538s]       |         [01;35m[K^~~~~~~[m[K
[5.538s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:334:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kperiod[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.538s]   334 |     unsigned char [01;35m[Kperiod[m[K = 0;
[5.538s]       |                   [01;35m[K^~~~~~[m[K
[5.538s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:329:21:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Karg[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
[5.538s]   329 | void* MapShow([01;35m[Kvoid *arg[m[K)
[5.538s]       |               [01;35m[K~~~~~~^~~[m[K
[5.555s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:[m[K In function ‘[01m[Kint main(int, char**)[m[K’:
[5.555s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:661:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.555s]   661 |                 [01;35m[K.[m[Kcommand = wp["command"].get<uint16_t>(),
[5.555s]       |                 [01;35m[K^[m[K
[5.563s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:662:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.563s]   662 |                 [01;35m[K.[m[Kseq = wp["seq"].get<uint8_t>(),
[5.564s]       |                 [01;35m[K^[m[K
[5.571s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:663:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.571s]   663 |                 [01;35m[K.[m[Kparam1 = wp["param1"].get<float>(),
[5.571s]       |                 [01;35m[K^[m[K
[5.580s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:664:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.580s]   664 |                 [01;35m[K.[m[Kparam2 = wp["param2"].get<float>(),
[5.580s]       |                 [01;35m[K^[m[K
[5.580s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:665:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.580s]   665 |                 [01;35m[K.[m[Kparam3 = wp["param3"].get<float>(),
[5.580s]       |                 [01;35m[K^[m[K
[5.580s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:666:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.580s]   666 |                 [01;35m[K.[m[Kparam4 = wp["param4"].get<float>(),
[5.581s]       |                 [01;35m[K^[m[K
[5.581s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:667:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.581s]   667 |                 [01;35m[K.[m[Klat = wp["x"].get<float>(),
[5.581s]       |                 [01;35m[K^[m[K
[5.582s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:668:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.582s]   668 |                 [01;35m[K.[m[Klon = wp["y"].get<float>(),
[5.582s]       |                 [01;35m[K^[m[K
[5.582s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:669:17:[m[K [01;35m[Kwarning: [m[KC++ designated initializers only available with ‘[01m[K-std=c++20[m[K’ or ‘[01m[K-std=gnu++20[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic-Wpedantic]8;;[m[K]
[5.582s]   669 |                 [01;35m[K.[m[Kalt = wp["z"].get<float>()
[5.583s]       |                 [01;35m[K^[m[K
[5.583s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:670:13:[m[K [01;35m[Kwarning: [m[Kmissing initializer for member ‘[01m[KMissionItem::reserve[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers-Wmissing-field-initializers]8;;[m[K]
[5.583s]   670 |             [01;35m[K}[m[K;
[5.583s]       |             [01;35m[K^[m[K
[5.584s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:683:21:[m[K [01;35m[Kwarning: [m[Kcomparison of integer expressions of different signedness: ‘[01m[Kint[m[K’ and ‘[01m[Kstd::vector<MissionItem>::size_type[m[K’ {aka ‘[01m[Klong unsigned int[m[K’} [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare-Wsign-compare]8;;[m[K]
[5.585s]   683 |     for(int i = 0; [01;35m[Ki<missions.size()[m[K; i++){
[5.585s]       |                    [01;35m[K~^~~~~~~~~~~~~~~~[m[K
[5.691s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1007:60:[m[K [01;35m[Kwarning: [m[Kunsigned conversion from ‘[01m[Kint[m[K’ to ‘[01m[Ku_int8_t[m[K’ {aka ‘[01m[Kunsigned char[m[K’} changes value from ‘[01m[K512[m[K’ to ‘[01m[K0[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow-Woverflow]8;;[m[K]
[5.691s]  1007 |                     taskAnchorMissionControl.mission_num = [01;35m[K512[m[K;
[5.691s]       |                                                            [01;35m[K^~~[m[K
[5.704s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1160:21:[m[K [01;31m[Kerror: [m[K‘[01m[KPosition[m[K’ was not declared in this scope
[5.704s]  1160 |                     [01;31m[KPosition[m[K currentPos = motionPlanner.GetCurrentPosition();
[5.704s]       |                     [01;31m[K^~~~~~~~[m[K
[5.705s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1161:58:[m[K [01;31m[Kerror: [m[K‘[01m[Kclass MotionPlan[m[K’ has no member named ‘[01m[KGetCurrentHeading[m[K’; did you mean ‘[01m[KtargetHeading[m[K’?
[5.705s]  1161 |                     float currentHeading = motionPlanner.[01;31m[KGetCurrentHeading[m[K();
[5.706s]       |                                                          [01;31m[K^~~~~~~~~~~~~~~~~[m[K
[5.706s]       |                                                          [32m[KtargetHeading[m[K
[5.706s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1162:55:[m[K [01;31m[Kerror: [m[K‘[01m[Kclass MotionPlan[m[K’ has no member named ‘[01m[KGetTargetVelocity[m[K’
[5.706s]  1162 |                     float targetSpeed = motionPlanner.[01;31m[KGetTargetVelocity[m[K();
[5.706s]       |                                                       [01;31m[K^~~~~~~~~~~~~~~~~[m[K
[5.706s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1185:61:[m[K [01;31m[Kerror: [m[K‘[01m[Kclass MotionPlan[m[K’ has no member named ‘[01m[KGetTargetHeading[m[K’; did you mean ‘[01m[KtargetHeading[m[K’?
[5.707s]  1185 |                         float targetHeading = motionPlanner.[01;31m[KGetTargetHeading[m[K();
[5.707s]       |                                                             [01;31m[K^~~~~~~~~~~~~~~~[m[K
[5.707s]       |                                                             [32m[KtargetHeading[m[K
[5.716s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1188:44:[m[K [01;31m[Kerror: [m[K‘[01m[KcurrentPos[m[K’ was not declared in this scope
[5.716s]  1188 |                             float checkX = [01;31m[KcurrentPos[m[K.x + checkDistance * cosf(targetHeading * M_PI / 180.0f);
[5.716s]       |                                            [01;31m[K^~~~~~~~~~[m[K
[5.731s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1191:33:[m[K [01;31m[Kerror: [m[K‘[01m[KCheckObstacleAtPosition[m[K’ was not declared in this scope
[5.731s]  1191 |                             if ([01;31m[KCheckObstacleAtPosition[m[K(checkX, checkY, 30.0f)) {
[5.731s]       |                                 [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~[m[K
[5.739s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1218:48:[m[K [01;31m[Kerror: [m[K‘[01m[KcurrentPos[m[K’ was not declared in this scope
[5.739s]  1218 |                                 float checkX = [01;31m[KcurrentPos[m[K.x + checkDistance * cosf(leftAngle * M_PI / 180.0f);
[5.739s]       |                                                [01;31m[K^~~~~~~~~~[m[K
[5.755s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1220:37:[m[K [01;31m[Kerror: [m[K‘[01m[KCheckObstacleAtPosition[m[K’ was not declared in this scope
[5.755s]  1220 |                                 if ([01;31m[KCheckObstacleAtPosition[m[K(checkX, checkY, 25.0f)) {
[5.755s]       |                                     [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~[m[K
[5.764s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1229:48:[m[K [01;31m[Kerror: [m[K‘[01m[KcurrentPos[m[K’ was not declared in this scope
[5.764s]  1229 |                                 float checkX = [01;31m[KcurrentPos[m[K.x + checkDistance * cosf(rightAngle * M_PI / 180.0f);
[5.764s]       |                                                [01;31m[K^~~~~~~~~~[m[K
[5.777s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1231:37:[m[K [01;31m[Kerror: [m[K‘[01m[KCheckObstacleAtPosition[m[K’ was not declared in this scope
[5.777s]  1231 |                                 if ([01;31m[KCheckObstacleAtPosition[m[K(checkX, checkY, 25.0f)) {
[5.777s]       |                                     [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~[m[K
[5.777s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1265:43:[m[K [01;31m[Kerror: [m[K‘[01m[Kclass MotionPlan[m[K’ has no member named ‘[01m[KSetTargetHeading[m[K’; did you mean ‘[01m[KtargetHeading[m[K’?
[5.777s]  1265 |                             motionPlanner.[01;31m[KSetTargetHeading[m[K(bestAvoidanceAngle);
[5.777s]       |                                           [01;31m[K^~~~~~~~~~~~~~~~[m[K
[5.777s]       |                                           [32m[KtargetHeading[m[K
[5.778s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1266:43:[m[K [01;31m[Kerror: [m[K‘[01m[Kclass MotionPlan[m[K’ has no member named ‘[01m[KSetTargetVelocity[m[K’
[5.778s]  1266 |                             motionPlanner.[01;31m[KSetTargetVelocity[m[K(adjustedSpeed);
[5.778s]       |                                           [01;31m[K^~~~~~~~~~~~~~~~~[m[K
[5.778s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1161:27:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[KcurrentHeading[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.778s]  1161 |                     float [01;35m[KcurrentHeading[m[K = motionPlanner.GetCurrentHeading();
[5.778s]       |                           [01;35m[K^~~~~~~~~~~~~~[m[K
[5.779s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:687:19:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.779s]   687 |     double start, [01;35m[Kend[m[K;
[5.780s]       |                   [01;35m[K^~~[m[K
[5.780s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:688:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kduration[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.780s]   688 |     double [01;35m[Kduration[m[K[10];
[5.780s]       |            [01;35m[K^~~~~~~~[m[K
[5.780s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:689:9:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdatanum[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.780s]   689 |     int [01;35m[Kdatanum[m[K = 0;
[5.780s]       |         [01;35m[K^~~~~~~[m[K
[5.780s] [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:731:12:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kanglevel[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
[5.780s]   731 |     double [01;35m[Kanglevel[m[K[2];
[5.780s]       |            [01;35m[K^~~~~~~~[m[K
[11.641s] gmake[2]: *** [CMakeFiles/xt_user_node.dir/build.make:76：CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o] 错误 1
[11.641s] gmake[1]: *** [CMakeFiles/Makefile2:165：CMakeFiles/xt_user_node.dir/all] 错误 2
[11.642s] gmake: *** [Makefile:146：all] 错误 2
[11.645s] Invoked command in '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new（复件）/USVControl-user/build/xt_user -- -j8 -l8
