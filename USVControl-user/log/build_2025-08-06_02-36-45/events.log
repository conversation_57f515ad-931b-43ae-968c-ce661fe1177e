[0.000000] (-) TimerEvent: {}
[0.001538] (xt_user) JobQueued: {'identifier': 'xt_user', 'dependencies': OrderedDict()}
[0.001604] (xt_user) JobStarted: {'identifier': 'xt_user'}
[0.007824] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'cmake'}
[0.008664] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'build'}
[0.009391] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/project2_new（复件）/USVControl-user'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1633'), ('SYSTEMD_EXEC_PID', '1812'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '7272'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:21315'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1776,unix/xumj-virtual-machine:/tmp/.ICE-unix/1776'), ('INVOCATION_ID', '427262512d8c42888457c1e9d867a052'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.2DR4A3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-6d8174bbac.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.022458] (xt_user) StderrLine: {'line': b'\x1b[0mCMake Error: The current CMakeCache.txt directory /home/<USER>/project2_new\xef\xbc\x88\xe5\xa4\x8d\xe4\xbb\xb6\xef\xbc\x89/USVControl-user/build/xt_user/CMakeCache.txt is different than the directory /home/<USER>/project2_new/USVControl-user/build/xt_user where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt\x1b[0m\n'}
[0.050154] (xt_user) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.100086] (-) TimerEvent: {}
[0.188789] (xt_user) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.200375] (-) TimerEvent: {}
[0.237334] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.241032] (xt_user) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.250017] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.262665] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.277345] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.301521] (-) TimerEvent: {}
[0.320359] (xt_user) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.326820] (xt_user) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.402768] (-) TimerEvent: {}
[0.434402] (xt_user) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.504286] (-) TimerEvent: {}
[0.507812] (xt_user) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[0.605613] (-) TimerEvent: {}
[0.629045] (xt_user) StdoutLine: {'line': b'-- Configuring done\n'}
[0.648414] (xt_user) StdoutLine: {'line': b'-- Generating done\n'}
[0.656329] (xt_user) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/project2_new/USVControl-user/build/xt_user\n'}
[0.685204] (xt_user) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target planning_hpp\x1b[0m\n'}
[0.698762] (xt_user) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o\x1b[0m\n'}
[0.698936] (xt_user) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/controller/motion_control.cpp.o\x1b[0m\n'}
[0.706342] (-) TimerEvent: {}
[0.808104] (-) TimerEvent: {}
[0.910026] (-) TimerEvent: {}
[1.010733] (-) TimerEvent: {}
[1.038215] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[KMotionPlan::MotionPlan()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.038455] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:36:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kvoid* memset(void*, int, size_t)\x1b[m\x1b[K\xe2\x80\x99 clearing an object of type \xe2\x80\x98\x1b[01m\x1b[Kstruct ObstacleAvoidanceParam\x1b[m\x1b[K\xe2\x80\x99 with no trivial copy-assignment; use assignment or value-initialization instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wclass-memaccess\x07-Wclass-memaccess\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.038522] (xt_user) StderrLine: {'line': b'   36 |         \x1b[01;35m\x1b[Kmemset(&obstacleAvoidance, 0, sizeof(ObstacleAvoidanceParam))\x1b[m\x1b[K;\n'}
[1.039220] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.039303] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[1.039370] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:48:1:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstruct ObstacleAvoidanceParam\x1b[m\x1b[K\xe2\x80\x99 declared here\n'}
[1.039432] (xt_user) StderrLine: {'line': b'   48 | \x1b[01;36m\x1b[K{\x1b[m\x1b[K\n'}
[1.039487] (xt_user) StderrLine: {'line': b'      | \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[1.041327] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.042099] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:261:43:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kboatstate\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.042195] (xt_user) StderrLine: {'line': b'  261 | void MotionPlan::TrajecotryGet(\x1b[01;35m\x1b[KTransState boatstate\x1b[m\x1b[K, anchor_mission_control task)\n'}
[1.042255] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;35m\x1b[K~~~~~~~~~~~^~~~~~~~~\x1b[m\x1b[K\n'}
[1.042317] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.042393] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:315:47:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[1]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.042467] (xt_user) StderrLine: {'line': b'  315 |         float pos[2] = { \x1b[01;35m\x1b[Kboatstate.PostoHome[1]\x1b[m\x1b[K, boatstate.PostoHome[0] }; // [X, Y] = [lon, lat]\n'}
[1.043789] (xt_user) StderrLine: {'line': b'      |                          \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[1.044160] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:315:71:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[0]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.044249] (xt_user) StderrLine: {'line': b'  315 |         float pos[2] = { boatstate.PostoHome[1], \x1b[01;35m\x1b[Kboatstate.PostoHome[0]\x1b[m\x1b[K }; // [X, Y] = [lon, lat]\n'}
[1.044310] (xt_user) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[1.044441] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:458:35:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kunsigned int\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.044511] (xt_user) StderrLine: {'line': b'  458 |     if (\x1b[01;35m\x1b[KdotTrackingParam.TrackNum > phaseTransitionFlag\x1b[m\x1b[K) {\n'}
[1.044576] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.060310] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::CalculateAvoidanceAngle(float*, float*, float)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.060598] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:647:49:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.060731] (xt_user) StderrLine: {'line': b'  647 | float MotionPlan::CalculateAvoidanceAngle(\x1b[01;35m\x1b[Kfloat currentPos[2]\x1b[m\x1b[K, float targetPos[2], float originalAngle)\n'}
[1.060802] (xt_user) StderrLine: {'line': b'      |                                           \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.060971] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:647:70:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KtargetPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.061054] (xt_user) StderrLine: {'line': b'  647 | float MotionPlan::CalculateAvoidanceAngle(float currentPos[2], \x1b[01;35m\x1b[Kfloat targetPos[2]\x1b[m\x1b[K, float originalAngle)\n'}
[1.061117] (xt_user) StderrLine: {'line': b'      |                                                                \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.062358] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool MotionPlan::CheckMissionFailure(float*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.062560] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:679:44:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.062651] (xt_user) StderrLine: {'line': b'  679 | bool MotionPlan::CheckMissionFailure(\x1b[01;35m\x1b[Kfloat currentPos[2]\x1b[m\x1b[K)\n'}
[1.062713] (xt_user) StderrLine: {'line': b'      |                                      \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.111051] (-) TimerEvent: {}
[1.213125] (-) TimerEvent: {}
[1.271009] (xt_user) StdoutLine: {'line': b'[ 42%] \x1b[32m\x1b[1mLinking CXX shared library libplanning_hpp.so\x1b[0m\n'}
[1.313859] (-) TimerEvent: {}
[1.397433] (xt_user) StdoutLine: {'line': b'[ 71%] Built target planning_hpp\n'}
[1.413417] (xt_user) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target xt_user_node\x1b[0m\n'}
[1.414382] (-) TimerEvent: {}
[1.438558] (xt_user) StdoutLine: {'line': b'[ 85%] \x1b[32mBuilding CXX object CMakeFiles/xt_user_node.dir/main/planning_main.cpp.o\x1b[0m\n'}
[1.515058] (-) TimerEvent: {}
[1.615651] (-) TimerEvent: {}
[1.717165] (-) TimerEvent: {}
[1.817786] (-) TimerEvent: {}
[1.918952] (-) TimerEvent: {}
[2.020744] (-) TimerEvent: {}
[2.121400] (-) TimerEvent: {}
[2.222661] (-) TimerEvent: {}
[2.323184] (-) TimerEvent: {}
[2.423727] (-) TimerEvent: {}
[2.525305] (-) TimerEvent: {}
[2.625895] (-) TimerEvent: {}
[2.727138] (-) TimerEvent: {}
[2.828029] (-) TimerEvent: {}
[2.929542] (-) TimerEvent: {}
[3.031210] (-) TimerEvent: {}
[3.133027] (-) TimerEvent: {}
[3.234942] (-) TimerEvent: {}
[3.336550] (-) TimerEvent: {}
[3.437473] (-) TimerEvent: {}
[3.539000] (-) TimerEvent: {}
[3.639463] (-) TimerEvent: {}
[3.739966] (-) TimerEvent: {}
[3.841896] (-) TimerEvent: {}
[3.942850] (-) TimerEvent: {}
[4.044404] (-) TimerEvent: {}
[4.144935] (-) TimerEvent: {}
[4.246213] (-) TimerEvent: {}
[4.347180] (-) TimerEvent: {}
[4.449232] (-) TimerEvent: {}
[4.551131] (-) TimerEvent: {}
[4.652835] (-) TimerEvent: {}
[4.753296] (-) TimerEvent: {}
[4.854240] (-) TimerEvent: {}
[4.955017] (-) TimerEvent: {}
[5.055987] (-) TimerEvent: {}
[5.157965] (-) TimerEvent: {}
[5.258411] (-) TimerEvent: {}
[5.359423] (-) TimerEvent: {}
[5.460503] (-) TimerEvent: {}
[5.532737] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid EnvironCallback(std_msgs::msg::UInt8MultiArray_<std::allocator<void> >::SharedPtr)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[5.532936] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:263:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.532998] (xt_user) StderrLine: {'line': b'  263 |     double \x1b[01;35m\x1b[Kstime\x1b[m\x1b[K, etime;\n'}
[5.533140] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[5.533205] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:263:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Ketime\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.533325] (xt_user) StderrLine: {'line': b'  263 |     double stime, \x1b[01;35m\x1b[Ketime\x1b[m\x1b[K;\n'}
[5.533362] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[5.533396] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kvoid* MapShow(void*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[5.533438] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:363:27:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Ksize_t\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} and \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.533475] (xt_user) StderrLine: {'line': b'  363 |                     if (\x1b[01;35m\x1b[Ki < current_target\x1b[m\x1b[K) {\n'}
[5.533509] (xt_user) StderrLine: {'line': b'      |                         \x1b[01;35m\x1b[K~~^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.533543] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:367:34:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Ksize_t\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} and \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.533586] (xt_user) StderrLine: {'line': b'  367 |                     } else if (\x1b[01;35m\x1b[Ki == current_target\x1b[m\x1b[K) {\n'}
[5.533620] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;35m\x1b[K~~^~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.534861] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:442:55:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<MissionItem>::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.534993] (xt_user) StderrLine: {'line': b'  442 |             if (\x1b[01;35m\x1b[KmotionPlanner.GetCurrentTargetIndex() < missions.size()\x1b[m\x1b[K) {\n'}
[5.535095] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.535143] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:458:55:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<MissionItem>::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.535181] (xt_user) StderrLine: {'line': b'  458 |             if (\x1b[01;35m\x1b[KmotionPlanner.GetCurrentTargetIndex() < missions.size()\x1b[m\x1b[K) {\n'}
[5.535215] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.537473] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:558:55:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<MissionItem>::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.537574] (xt_user) StderrLine: {'line': b'  558 |             if (\x1b[01;35m\x1b[KmotionPlanner.GetCurrentTargetIndex() < missions.size()\x1b[m\x1b[K) {\n'}
[5.537613] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.538871] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kvariable \xe2\x80\x98\x1b[01m\x1b[Kstart\x1b[m\x1b[K\xe2\x80\x99 set but not used [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-but-set-variable\x07-Wunused-but-set-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.539006] (xt_user) StderrLine: {'line': b'  331 |     double \x1b[01;35m\x1b[Kstart\x1b[m\x1b[K,end;\n'}
[5.539051] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[5.539111] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:331:18:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.539164] (xt_user) StderrLine: {'line': b'  331 |     double start,\x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[5.539200] (xt_user) StderrLine: {'line': b'      |                  \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[5.539235] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:332:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.539272] (xt_user) StderrLine: {'line': b'  332 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[5.539305] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[5.539338] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:334:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kperiod\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.539379] (xt_user) StderrLine: {'line': b'  334 |     unsigned char \x1b[01;35m\x1b[Kperiod\x1b[m\x1b[K = 0;\n'}
[5.539411] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~~~~\x1b[m\x1b[K\n'}
[5.539699] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:329:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Karg\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.539771] (xt_user) StderrLine: {'line': b'  329 | void* MapShow(\x1b[01;35m\x1b[Kvoid *arg\x1b[m\x1b[K)\n'}
[5.539806] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K~~~~~~^~~\x1b[m\x1b[K\n'}
[5.556274] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:\x1b[m\x1b[K In function \xe2\x80\x98\x1b[01m\x1b[Kint main(int, char**)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[5.556467] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:661:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.556533] (xt_user) StderrLine: {'line': b'  661 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kcommand = wp["command"].get<uint16_t>(),\n'}
[5.556570] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.561813] (-) TimerEvent: {}
[5.564821] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:662:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.565053] (xt_user) StderrLine: {'line': b'  662 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kseq = wp["seq"].get<uint8_t>(),\n'}
[5.565109] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.571970] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:663:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.572246] (xt_user) StderrLine: {'line': b'  663 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam1 = wp["param1"].get<float>(),\n'}
[5.572307] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.581075] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:664:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.581263] (xt_user) StderrLine: {'line': b'  664 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam2 = wp["param2"].get<float>(),\n'}
[5.581317] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.581515] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:665:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.581583] (xt_user) StderrLine: {'line': b'  665 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam3 = wp["param3"].get<float>(),\n'}
[5.581618] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.581944] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:666:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.582080] (xt_user) StderrLine: {'line': b'  666 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kparam4 = wp["param4"].get<float>(),\n'}
[5.582125] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.582274] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:667:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.582325] (xt_user) StderrLine: {'line': b'  667 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klat = wp["x"].get<float>(),\n'}
[5.582368] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.583445] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:668:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.583536] (xt_user) StderrLine: {'line': b'  668 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Klon = wp["y"].get<float>(),\n'}
[5.583631] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.583883] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:669:17:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[KC++ designated initializers only available with \xe2\x80\x98\x1b[01m\x1b[K-std=c++20\x1b[m\x1b[K\xe2\x80\x99 or \xe2\x80\x98\x1b[01m\x1b[K-std=gnu++20\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wpedantic\x07-Wpedantic\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.583958] (xt_user) StderrLine: {'line': b'  669 |                 \x1b[01;35m\x1b[K.\x1b[m\x1b[Kalt = wp["z"].get<float>()\n'}
[5.584075] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.584278] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:670:13:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kmissing initializer for member \xe2\x80\x98\x1b[01m\x1b[KMissionItem::reserve\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wmissing-field-initializers\x07-Wmissing-field-initializers\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.584363] (xt_user) StderrLine: {'line': b'  670 |             \x1b[01;35m\x1b[K}\x1b[m\x1b[K;\n'}
[5.584439] (xt_user) StderrLine: {'line': b'      |             \x1b[01;35m\x1b[K^\x1b[m\x1b[K\n'}
[5.585958] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:683:21:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kcomparison of integer expressions of different signedness: \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 and \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<MissionItem>::size_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Klong unsigned int\x1b[m\x1b[K\xe2\x80\x99} [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wsign-compare\x07-Wsign-compare\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.586148] (xt_user) StderrLine: {'line': b'  683 |     for(int i = 0; \x1b[01;35m\x1b[Ki<missions.size()\x1b[m\x1b[K; i++){\n'}
[5.586188] (xt_user) StderrLine: {'line': b'      |                    \x1b[01;35m\x1b[K~^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.662828] (-) TimerEvent: {}
[5.692103] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1007:60:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunsigned conversion from \xe2\x80\x98\x1b[01m\x1b[Kint\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Ku_int8_t\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kunsigned char\x1b[m\x1b[K\xe2\x80\x99} changes value from \xe2\x80\x98\x1b[01m\x1b[K512\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[K0\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Woverflow\x07-Woverflow\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.692267] (xt_user) StderrLine: {'line': b' 1007 |                     taskAnchorMissionControl.mission_num = \x1b[01;35m\x1b[K512\x1b[m\x1b[K;\n'}
[5.692310] (xt_user) StderrLine: {'line': b'      |                                                            \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[5.705304] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1160:21:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KPosition\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[5.705518] (xt_user) StderrLine: {'line': b' 1160 |                     \x1b[01;31m\x1b[KPosition\x1b[m\x1b[K currentPos = motionPlanner.GetCurrentPosition();\n'}
[5.705565] (xt_user) StderrLine: {'line': b'      |                     \x1b[01;31m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[5.706887] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1161:58:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass MotionPlan\x1b[m\x1b[K\xe2\x80\x99 has no member named \xe2\x80\x98\x1b[01m\x1b[KGetCurrentHeading\x1b[m\x1b[K\xe2\x80\x99; did you mean \xe2\x80\x98\x1b[01m\x1b[KtargetHeading\x1b[m\x1b[K\xe2\x80\x99?\n'}
[5.707083] (xt_user) StderrLine: {'line': b' 1161 |                     float currentHeading = motionPlanner.\x1b[01;31m\x1b[KGetCurrentHeading\x1b[m\x1b[K();\n'}
[5.707147] (xt_user) StderrLine: {'line': b'      |                                                          \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.707203] (xt_user) StderrLine: {'line': b'      |                                                          \x1b[32m\x1b[KtargetHeading\x1b[m\x1b[K\n'}
[5.707258] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1162:55:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass MotionPlan\x1b[m\x1b[K\xe2\x80\x99 has no member named \xe2\x80\x98\x1b[01m\x1b[KGetTargetVelocity\x1b[m\x1b[K\xe2\x80\x99\n'}
[5.707365] (xt_user) StderrLine: {'line': b' 1162 |                     float targetSpeed = motionPlanner.\x1b[01;31m\x1b[KGetTargetVelocity\x1b[m\x1b[K();\n'}
[5.707495] (xt_user) StderrLine: {'line': b'      |                                                       \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.707978] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1185:61:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass MotionPlan\x1b[m\x1b[K\xe2\x80\x99 has no member named \xe2\x80\x98\x1b[01m\x1b[KGetTargetHeading\x1b[m\x1b[K\xe2\x80\x99; did you mean \xe2\x80\x98\x1b[01m\x1b[KtargetHeading\x1b[m\x1b[K\xe2\x80\x99?\n'}
[5.708112] (xt_user) StderrLine: {'line': b' 1185 |                         float targetHeading = motionPlanner.\x1b[01;31m\x1b[KGetTargetHeading\x1b[m\x1b[K();\n'}
[5.708217] (xt_user) StderrLine: {'line': b'      |                                                             \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.708301] (xt_user) StderrLine: {'line': b'      |                                                             \x1b[32m\x1b[KtargetHeading\x1b[m\x1b[K\n'}
[5.717317] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1188:44:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[5.717932] (xt_user) StderrLine: {'line': b' 1188 |                             float checkX = \x1b[01;31m\x1b[KcurrentPos\x1b[m\x1b[K.x + checkDistance * cosf(targetHeading * M_PI / 180.0f);\n'}
[5.717989] (xt_user) StderrLine: {'line': b'      |                                            \x1b[01;31m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[5.732862] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1191:33:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KCheckObstacleAtPosition\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[5.733027] (xt_user) StderrLine: {'line': b' 1191 |                             if (\x1b[01;31m\x1b[KCheckObstacleAtPosition\x1b[m\x1b[K(checkX, checkY, 30.0f)) {\n'}
[5.733088] (xt_user) StderrLine: {'line': b'      |                                 \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.740342] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1218:48:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[5.740621] (xt_user) StderrLine: {'line': b' 1218 |                                 float checkX = \x1b[01;31m\x1b[KcurrentPos\x1b[m\x1b[K.x + checkDistance * cosf(leftAngle * M_PI / 180.0f);\n'}
[5.740732] (xt_user) StderrLine: {'line': b'      |                                                \x1b[01;31m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[5.756160] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1220:37:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KCheckObstacleAtPosition\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[5.756406] (xt_user) StderrLine: {'line': b' 1220 |                                 if (\x1b[01;31m\x1b[KCheckObstacleAtPosition\x1b[m\x1b[K(checkX, checkY, 25.0f)) {\n'}
[5.756473] (xt_user) StderrLine: {'line': b'      |                                     \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.763273] (-) TimerEvent: {}
[5.765348] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1229:48:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[5.765544] (xt_user) StderrLine: {'line': b' 1229 |                                 float checkX = \x1b[01;31m\x1b[KcurrentPos\x1b[m\x1b[K.x + checkDistance * cosf(rightAngle * M_PI / 180.0f);\n'}
[5.765603] (xt_user) StderrLine: {'line': b'      |                                                \x1b[01;31m\x1b[K^~~~~~~~~~\x1b[m\x1b[K\n'}
[5.778246] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1231:37:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KCheckObstacleAtPosition\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[5.778463] (xt_user) StderrLine: {'line': b' 1231 |                                 if (\x1b[01;31m\x1b[KCheckObstacleAtPosition\x1b[m\x1b[K(checkX, checkY, 25.0f)) {\n'}
[5.778523] (xt_user) StderrLine: {'line': b'      |                                     \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.778577] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1265:43:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass MotionPlan\x1b[m\x1b[K\xe2\x80\x99 has no member named \xe2\x80\x98\x1b[01m\x1b[KSetTargetHeading\x1b[m\x1b[K\xe2\x80\x99; did you mean \xe2\x80\x98\x1b[01m\x1b[KtargetHeading\x1b[m\x1b[K\xe2\x80\x99?\n'}
[5.778646] (xt_user) StderrLine: {'line': b' 1265 |                             motionPlanner.\x1b[01;31m\x1b[KSetTargetHeading\x1b[m\x1b[K(bestAvoidanceAngle);\n'}
[5.778699] (xt_user) StderrLine: {'line': b'      |                                           \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.778749] (xt_user) StderrLine: {'line': b'      |                                           \x1b[32m\x1b[KtargetHeading\x1b[m\x1b[K\n'}
[5.778797] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1266:43:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kclass MotionPlan\x1b[m\x1b[K\xe2\x80\x99 has no member named \xe2\x80\x98\x1b[01m\x1b[KSetTargetVelocity\x1b[m\x1b[K\xe2\x80\x99\n'}
[5.779379] (xt_user) StderrLine: {'line': b' 1266 |                             motionPlanner.\x1b[01;31m\x1b[KSetTargetVelocity\x1b[m\x1b[K(adjustedSpeed);\n'}
[5.779552] (xt_user) StderrLine: {'line': b'      |                                           \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.779636] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:1161:27:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[KcurrentHeading\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.779695] (xt_user) StderrLine: {'line': b' 1161 |                     float \x1b[01;35m\x1b[KcurrentHeading\x1b[m\x1b[K = motionPlanner.GetCurrentHeading();\n'}
[5.779734] (xt_user) StderrLine: {'line': b'      |                           \x1b[01;35m\x1b[K^~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[5.780637] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:687:19:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.780997] (xt_user) StderrLine: {'line': b'  687 |     double start, \x1b[01;35m\x1b[Kend\x1b[m\x1b[K;\n'}
[5.781106] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;35m\x1b[K^~~\x1b[m\x1b[K\n'}
[5.781164] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:688:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kduration\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.781222] (xt_user) StderrLine: {'line': b'  688 |     double \x1b[01;35m\x1b[Kduration\x1b[m\x1b[K[10];\n'}
[5.781275] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[5.781328] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:689:9:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdatanum\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.781396] (xt_user) StderrLine: {'line': b'  689 |     int \x1b[01;35m\x1b[Kdatanum\x1b[m\x1b[K = 0;\n'}
[5.781458] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[5.781514] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/main/planning_main.cpp:731:12:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kanglevel\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[5.781597] (xt_user) StderrLine: {'line': b'  731 |     double \x1b[01;35m\x1b[Kanglevel\x1b[m\x1b[K[2];\n'}
[5.781724] (xt_user) StderrLine: {'line': b'      |            \x1b[01;35m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[5.864113] (-) TimerEvent: {}
[5.966026] (-) TimerEvent: {}
[6.066998] (-) TimerEvent: {}
[6.169105] (-) TimerEvent: {}
[6.271065] (-) TimerEvent: {}
[6.371997] (-) TimerEvent: {}
[6.473824] (-) TimerEvent: {}
[6.574822] (-) TimerEvent: {}
[6.676095] (-) TimerEvent: {}
[6.776988] (-) TimerEvent: {}
[6.877842] (-) TimerEvent: {}
[6.978799] (-) TimerEvent: {}
[7.083337] (-) TimerEvent: {}
[7.183943] (-) TimerEvent: {}
[7.286112] (-) TimerEvent: {}
[7.387307] (-) TimerEvent: {}
[7.488391] (-) TimerEvent: {}
[7.591686] (-) TimerEvent: {}
[7.693685] (-) TimerEvent: {}
[7.794169] (-) TimerEvent: {}
[7.894780] (-) TimerEvent: {}
[7.995570] (-) TimerEvent: {}
[8.096065] (-) TimerEvent: {}
[8.196487] (-) TimerEvent: {}
[8.297714] (-) TimerEvent: {}
[8.398312] (-) TimerEvent: {}
[8.498752] (-) TimerEvent: {}
[8.600047] (-) TimerEvent: {}
[8.700747] (-) TimerEvent: {}
[8.801150] (-) TimerEvent: {}
[8.901722] (-) TimerEvent: {}
[9.003806] (-) TimerEvent: {}
[9.107795] (-) TimerEvent: {}
[9.208466] (-) TimerEvent: {}
[9.309150] (-) TimerEvent: {}
[9.409933] (-) TimerEvent: {}
[9.515640] (-) TimerEvent: {}
[9.616044] (-) TimerEvent: {}
[9.716614] (-) TimerEvent: {}
[9.817093] (-) TimerEvent: {}
[9.917685] (-) TimerEvent: {}
[10.018498] (-) TimerEvent: {}
[10.120064] (-) TimerEvent: {}
[10.220812] (-) TimerEvent: {}
[10.321141] (-) TimerEvent: {}
[10.421530] (-) TimerEvent: {}
[10.523346] (-) TimerEvent: {}
[10.624601] (-) TimerEvent: {}
[10.726362] (-) TimerEvent: {}
[10.827660] (-) TimerEvent: {}
[10.928148] (-) TimerEvent: {}
[11.029711] (-) TimerEvent: {}
[11.130917] (-) TimerEvent: {}
[11.232342] (-) TimerEvent: {}
[11.333167] (-) TimerEvent: {}
[11.434842] (-) TimerEvent: {}
[11.535523] (-) TimerEvent: {}
[11.636471] (-) TimerEvent: {}
[11.642384] (xt_user) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/xt_user_node.dir/build.make:76\xef\xbc\x9aCMakeFiles/xt_user_node.dir/main/planning_main.cpp.o] \xe9\x94\x99\xe8\xaf\xaf 1\n'}
[11.642803] (xt_user) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:165\xef\xbc\x9aCMakeFiles/xt_user_node.dir/all] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[11.643087] (xt_user) StderrLine: {'line': b'gmake: *** [Makefile:146\xef\xbc\x9aall] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[11.646633] (xt_user) CommandEnded: {'returncode': 2}
[11.665545] (xt_user) JobEnded: {'identifier': 'xt_user', 'rc': 2}
[11.676384] (-) EventReactorShutdown: {}
