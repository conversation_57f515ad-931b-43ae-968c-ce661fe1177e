[0.441s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.441s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x796abd52f130>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x796abd674640>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x796abd674640>>)
[1.055s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.055s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.056s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.056s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.056s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.056s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.056s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/project2_new（复件）/USVControl-user'
[1.056s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.056s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.056s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.056s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.056s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.057s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.057s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.057s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.057s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.112s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ignore', 'ignore_ament_install']
[1.112s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ignore_ament_install'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_pkg']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_pkg'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['colcon_meta']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'colcon_meta'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['ros']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'ros'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['cmake', 'python']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'cmake'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extensions ['python_setup_py']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode) by extension 'python_setup_py'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ignore', 'ignore_ament_install']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ignore_ament_install'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_pkg']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_pkg'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['colcon_meta']
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'colcon_meta'
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['ros']
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'ros'
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['cmake', 'python']
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'cmake'
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python'
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extensions ['python_setup_py']
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/include) by extension 'python_setup_py'
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ignore', 'ignore_ament_install']
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore'
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ignore_ament_install'
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_pkg']
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_pkg'
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['colcon_meta']
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'colcon_meta'
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extensions ['ros']
[1.114s] Level 1:colcon.colcon_core.package_identification:_identify(ControlNode/planning) by extension 'ros'
[1.120s] DEBUG:colcon.colcon_core.package_identification:Package 'ControlNode/planning' with type 'ros.ament_cmake' and name 'xt_user'
[1.120s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.120s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.120s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ignore', 'ignore_ament_install']
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore'
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ignore_ament_install'
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_pkg']
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_pkg'
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['colcon_meta']
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'colcon_meta'
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['ros']
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'ros'
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['cmake', 'python']
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'cmake'
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python'
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extensions ['python_setup_py']
[1.121s] Level 1:colcon.colcon_core.package_identification:_identify(launch) by extension 'python_setup_py'
[1.122s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ignore', 'ignore_ament_install']
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore'
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ignore_ament_install'
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_pkg']
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_pkg'
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['colcon_meta']
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'colcon_meta'
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['ros']
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'ros'
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['cmake', 'python']
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'cmake'
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python'
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extensions ['python_setup_py']
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(launch/__pycache__) by extension 'python_setup_py'
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['ignore', 'ignore_ament_install']
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'ignore'
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'ignore_ament_install'
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['colcon_pkg']
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'colcon_pkg'
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['colcon_meta']
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'colcon_meta'
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['ros']
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'ros'
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['cmake', 'python']
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'cmake'
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'python'
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extensions ['python_setup_py']
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250709_132856) by extension 'python_setup_py'
[1.125s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['ignore', 'ignore_ament_install']
[1.126s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'ignore'
[1.126s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'ignore_ament_install'
[1.126s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['colcon_pkg']
[1.126s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'colcon_pkg'
[1.126s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['colcon_meta']
[1.126s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'colcon_meta'
[1.126s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['ros']
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'ros'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['cmake', 'python']
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'cmake'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'python'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extensions ['python_setup_py']
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104728) by extension 'python_setup_py'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['ignore', 'ignore_ament_install']
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'ignore'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'ignore_ament_install'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['colcon_pkg']
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'colcon_pkg'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['colcon_meta']
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'colcon_meta'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['ros']
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'ros'
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['cmake', 'python']
[1.127s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'cmake'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'python'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extensions ['python_setup_py']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_104847) by extension 'python_setup_py'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['ignore', 'ignore_ament_install']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'ignore'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'ignore_ament_install'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['colcon_pkg']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'colcon_pkg'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['colcon_meta']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'colcon_meta'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['ros']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'ros'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['cmake', 'python']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'cmake'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'python'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extensions ['python_setup_py']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_105652) by extension 'python_setup_py'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['ignore', 'ignore_ament_install']
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'ignore'
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'ignore_ament_install'
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['colcon_pkg']
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'colcon_pkg'
[1.129s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['colcon_meta']
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'colcon_meta'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['ros']
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'ros'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['cmake', 'python']
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'cmake'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'python'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extensions ['python_setup_py']
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_110230) by extension 'python_setup_py'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['ignore', 'ignore_ament_install']
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'ignore'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'ignore_ament_install'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['colcon_pkg']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'colcon_pkg'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['colcon_meta']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'colcon_meta'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['ros']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'ros'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['cmake', 'python']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'cmake'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'python'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extensions ['python_setup_py']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111022) by extension 'python_setup_py'
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['ignore', 'ignore_ament_install']
[1.131s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'ignore'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'ignore_ament_install'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['colcon_pkg']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'colcon_pkg'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['colcon_meta']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'colcon_meta'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['ros']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'ros'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['cmake', 'python']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'cmake'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'python'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extensions ['python_setup_py']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_111434) by extension 'python_setup_py'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['ignore', 'ignore_ament_install']
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'ignore'
[1.132s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'ignore_ament_install'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['colcon_pkg']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'colcon_pkg'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['colcon_meta']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'colcon_meta'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['ros']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'ros'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['cmake', 'python']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'cmake'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'python'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extensions ['python_setup_py']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112552) by extension 'python_setup_py'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['ignore', 'ignore_ament_install']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'ignore'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'ignore_ament_install'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['colcon_pkg']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'colcon_pkg'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['colcon_meta']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'colcon_meta'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['ros']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'ros'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['cmake', 'python']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'cmake'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'python'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extensions ['python_setup_py']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_112632) by extension 'python_setup_py'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['ignore', 'ignore_ament_install']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'ignore'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'ignore_ament_install'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['colcon_pkg']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'colcon_pkg'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['colcon_meta']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'colcon_meta'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['ros']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'ros'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['cmake', 'python']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'cmake'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'python'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extensions ['python_setup_py']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_120633) by extension 'python_setup_py'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['ignore', 'ignore_ament_install']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'ignore'
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'ignore_ament_install'
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['colcon_pkg']
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'colcon_pkg'
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['colcon_meta']
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'colcon_meta'
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['ros']
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'ros'
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['cmake', 'python']
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'cmake'
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'python'
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extensions ['python_setup_py']
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_132837) by extension 'python_setup_py'
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['ignore', 'ignore_ament_install']
[1.142s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'ignore'
[1.142s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'ignore_ament_install'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['colcon_pkg']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'colcon_pkg'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['colcon_meta']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'colcon_meta'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['ros']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'ros'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['cmake', 'python']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'cmake'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'python'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extensions ['python_setup_py']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133033) by extension 'python_setup_py'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['ignore', 'ignore_ament_install']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'ignore'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'ignore_ament_install'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['colcon_pkg']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'colcon_pkg'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['colcon_meta']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'colcon_meta'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['ros']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'ros'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['cmake', 'python']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'cmake'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'python'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extensions ['python_setup_py']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133338) by extension 'python_setup_py'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['ignore', 'ignore_ament_install']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'ignore'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'ignore_ament_install'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['colcon_pkg']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'colcon_pkg'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['colcon_meta']
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'colcon_meta'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['ros']
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'ros'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['cmake', 'python']
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'cmake'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'python'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extensions ['python_setup_py']
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133612) by extension 'python_setup_py'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['ignore', 'ignore_ament_install']
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'ignore'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'ignore_ament_install'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['colcon_pkg']
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'colcon_pkg'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['colcon_meta']
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'colcon_meta'
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['ros']
[1.145s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'ros'
[1.151s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['cmake', 'python']
[1.151s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'cmake'
[1.151s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'python'
[1.151s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extensions ['python_setup_py']
[1.152s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_133911) by extension 'python_setup_py'
[1.152s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['ignore', 'ignore_ament_install']
[1.152s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'ignore'
[1.152s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'ignore_ament_install'
[1.152s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['colcon_pkg']
[1.152s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'colcon_pkg'
[1.152s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['colcon_meta']
[1.152s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'colcon_meta'
[1.152s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['ros']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'ros'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['cmake', 'python']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'cmake'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'python'
[1.157s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extensions ['python_setup_py']
[1.157s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_134409) by extension 'python_setup_py'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['ignore', 'ignore_ament_install']
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'ignore'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'ignore_ament_install'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['colcon_pkg']
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'colcon_pkg'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['colcon_meta']
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'colcon_meta'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['ros']
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'ros'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['cmake', 'python']
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'cmake'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'python'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extensions ['python_setup_py']
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145736) by extension 'python_setup_py'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['ignore', 'ignore_ament_install']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'ignore'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'ignore_ament_install'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['colcon_pkg']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'colcon_pkg'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['colcon_meta']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'colcon_meta'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['ros']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'ros'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['cmake', 'python']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'cmake'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'python'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extensions ['python_setup_py']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_145938) by extension 'python_setup_py'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['ignore', 'ignore_ament_install']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'ignore'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'ignore_ament_install'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['colcon_pkg']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'colcon_pkg'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['colcon_meta']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'colcon_meta'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['ros']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'ros'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['cmake', 'python']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'cmake'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'python'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extensions ['python_setup_py']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150646) by extension 'python_setup_py'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['ignore', 'ignore_ament_install']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'ignore'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'ignore_ament_install'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['colcon_pkg']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'colcon_pkg'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['colcon_meta']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'colcon_meta'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['ros']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'ros'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['cmake', 'python']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'cmake'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'python'
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extensions ['python_setup_py']
[1.160s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_150855) by extension 'python_setup_py'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['ignore', 'ignore_ament_install']
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'ignore'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'ignore_ament_install'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['colcon_pkg']
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'colcon_pkg'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['colcon_meta']
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'colcon_meta'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['ros']
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'ros'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['cmake', 'python']
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'cmake'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'python'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extensions ['python_setup_py']
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151324) by extension 'python_setup_py'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['ignore', 'ignore_ament_install']
[1.166s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'ignore'
[1.167s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'ignore_ament_install'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['colcon_pkg']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'colcon_pkg'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['colcon_meta']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'colcon_meta'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['ros']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'ros'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['cmake', 'python']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'cmake'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'python'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extensions ['python_setup_py']
[1.172s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151454) by extension 'python_setup_py'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['ignore', 'ignore_ament_install']
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'ignore'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'ignore_ament_install'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['colcon_pkg']
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'colcon_pkg'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['colcon_meta']
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'colcon_meta'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['ros']
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'ros'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['cmake', 'python']
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'cmake'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'python'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extensions ['python_setup_py']
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151700) by extension 'python_setup_py'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['ignore', 'ignore_ament_install']
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'ignore'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'ignore_ament_install'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['colcon_pkg']
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'colcon_pkg'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['colcon_meta']
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'colcon_meta'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['ros']
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'ros'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['cmake', 'python']
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'cmake'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'python'
[1.174s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extensions ['python_setup_py']
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151825) by extension 'python_setup_py'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['ignore', 'ignore_ament_install']
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'ignore'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'ignore_ament_install'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['colcon_pkg']
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'colcon_pkg'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['colcon_meta']
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'colcon_meta'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['ros']
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'ros'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['cmake', 'python']
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'cmake'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'python'
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extensions ['python_setup_py']
[1.175s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_151959) by extension 'python_setup_py'
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['ignore', 'ignore_ament_install']
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'ignore'
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'ignore_ament_install'
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['colcon_pkg']
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'colcon_pkg'
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['colcon_meta']
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'colcon_meta'
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['ros']
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'ros'
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['cmake', 'python']
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'cmake'
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'python'
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extensions ['python_setup_py']
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_152254) by extension 'python_setup_py'
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['ignore', 'ignore_ament_install']
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'ignore'
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'ignore_ament_install'
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['colcon_pkg']
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'colcon_pkg'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['colcon_meta']
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'colcon_meta'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['ros']
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'ros'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['cmake', 'python']
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'cmake'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'python'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extensions ['python_setup_py']
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153221) by extension 'python_setup_py'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['ignore', 'ignore_ament_install']
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'ignore'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'ignore_ament_install'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['colcon_pkg']
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'colcon_pkg'
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['colcon_meta']
[1.180s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'colcon_meta'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['ros']
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'ros'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['cmake', 'python']
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'cmake'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'python'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extensions ['python_setup_py']
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153520) by extension 'python_setup_py'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['ignore', 'ignore_ament_install']
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'ignore'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'ignore_ament_install'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['colcon_pkg']
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'colcon_pkg'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['colcon_meta']
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'colcon_meta'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['ros']
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'ros'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['cmake', 'python']
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'cmake'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'python'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extensions ['python_setup_py']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_153816) by extension 'python_setup_py'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['ignore', 'ignore_ament_install']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'ignore'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'ignore_ament_install'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['colcon_pkg']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'colcon_pkg'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['colcon_meta']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'colcon_meta'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['ros']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'ros'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['cmake', 'python']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'cmake'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'python'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extensions ['python_setup_py']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_154402) by extension 'python_setup_py'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['ignore', 'ignore_ament_install']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'ignore'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'ignore_ament_install'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['colcon_pkg']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'colcon_pkg'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['colcon_meta']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'colcon_meta'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['ros']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'ros'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['cmake', 'python']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'cmake'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'python'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extensions ['python_setup_py']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160339) by extension 'python_setup_py'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['ignore', 'ignore_ament_install']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'ignore'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'ignore_ament_install'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['colcon_pkg']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'colcon_pkg'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['colcon_meta']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'colcon_meta'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['ros']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'ros'
[1.184s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['cmake', 'python']
[1.184s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'cmake'
[1.184s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'python'
[1.184s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extensions ['python_setup_py']
[1.184s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160505) by extension 'python_setup_py'
[1.184s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['ignore', 'ignore_ament_install']
[1.184s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'ignore'
[1.185s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'ignore_ament_install'
[1.185s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['colcon_pkg']
[1.185s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'colcon_pkg'
[1.186s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['colcon_meta']
[1.186s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'colcon_meta'
[1.186s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['ros']
[1.186s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'ros'
[1.186s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['cmake', 'python']
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'cmake'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'python'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extensions ['python_setup_py']
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_160950) by extension 'python_setup_py'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['ignore', 'ignore_ament_install']
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'ignore'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'ignore_ament_install'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['colcon_pkg']
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'colcon_pkg'
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['colcon_meta']
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'colcon_meta'
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['ros']
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'ros'
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['cmake', 'python']
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'cmake'
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'python'
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extensions ['python_setup_py']
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162013) by extension 'python_setup_py'
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['ignore', 'ignore_ament_install']
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'ignore'
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'ignore_ament_install'
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['colcon_pkg']
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'colcon_pkg'
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['colcon_meta']
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'colcon_meta'
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['ros']
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'ros'
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['cmake', 'python']
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'cmake'
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'python'
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extensions ['python_setup_py']
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_162115) by extension 'python_setup_py'
[1.189s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['ignore', 'ignore_ament_install']
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'ignore'
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'ignore_ament_install'
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['colcon_pkg']
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'colcon_pkg'
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['colcon_meta']
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'colcon_meta'
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['ros']
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'ros'
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['cmake', 'python']
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'cmake'
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'python'
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extensions ['python_setup_py']
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184344) by extension 'python_setup_py'
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['ignore', 'ignore_ament_install']
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'ignore'
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'ignore_ament_install'
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['colcon_pkg']
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'colcon_pkg'
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['colcon_meta']
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'colcon_meta'
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['ros']
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'ros'
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['cmake', 'python']
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'cmake'
[1.191s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'python'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extensions ['python_setup_py']
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_184721) by extension 'python_setup_py'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['ignore', 'ignore_ament_install']
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'ignore'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'ignore_ament_install'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['colcon_pkg']
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'colcon_pkg'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['colcon_meta']
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'colcon_meta'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['ros']
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'ros'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['cmake', 'python']
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'cmake'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'python'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extensions ['python_setup_py']
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191116) by extension 'python_setup_py'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['ignore', 'ignore_ament_install']
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'ignore'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'ignore_ament_install'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['colcon_pkg']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'colcon_pkg'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['colcon_meta']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'colcon_meta'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['ros']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'ros'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['cmake', 'python']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'cmake'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'python'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extensions ['python_setup_py']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_191452) by extension 'python_setup_py'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['ignore', 'ignore_ament_install']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'ignore'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'ignore_ament_install'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['colcon_pkg']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'colcon_pkg'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['colcon_meta']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'colcon_meta'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['ros']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'ros'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['cmake', 'python']
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'cmake'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'python'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extensions ['python_setup_py']
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_231849) by extension 'python_setup_py'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['ignore', 'ignore_ament_install']
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'ignore'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'ignore_ament_install'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['colcon_pkg']
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'colcon_pkg'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['colcon_meta']
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'colcon_meta'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['ros']
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'ros'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['cmake', 'python']
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'cmake'
[1.194s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'python'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extensions ['python_setup_py']
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_232938) by extension 'python_setup_py'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['ignore', 'ignore_ament_install']
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'ignore'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'ignore_ament_install'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['colcon_pkg']
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'colcon_pkg'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['colcon_meta']
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'colcon_meta'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['ros']
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'ros'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['cmake', 'python']
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'cmake'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'python'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extensions ['python_setup_py']
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233059) by extension 'python_setup_py'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['ignore', 'ignore_ament_install']
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'ignore'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'ignore_ament_install'
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['colcon_pkg']
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'colcon_pkg'
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['colcon_meta']
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'colcon_meta'
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['ros']
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'ros'
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['cmake', 'python']
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'cmake'
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'python'
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extensions ['python_setup_py']
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233346) by extension 'python_setup_py'
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['ignore', 'ignore_ament_install']
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'ignore'
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'ignore_ament_install'
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['colcon_pkg']
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'colcon_pkg'
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['colcon_meta']
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'colcon_meta'
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['ros']
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'ros'
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['cmake', 'python']
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'cmake'
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'python'
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extensions ['python_setup_py']
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_233813) by extension 'python_setup_py'
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['ignore', 'ignore_ament_install']
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'ignore'
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'ignore_ament_install'
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['colcon_pkg']
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'colcon_pkg'
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['colcon_meta']
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'colcon_meta'
[1.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['ros']
[1.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'ros'
[1.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['cmake', 'python']
[1.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'cmake'
[1.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'python'
[1.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extensions ['python_setup_py']
[1.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250711_234011) by extension 'python_setup_py'
[1.199s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['ignore', 'ignore_ament_install']
[1.200s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'ignore'
[1.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'ignore_ament_install'
[1.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['colcon_pkg']
[1.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'colcon_pkg'
[1.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['colcon_meta']
[1.201s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'colcon_meta'
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['ros']
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'ros'
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['cmake', 'python']
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'cmake'
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'python'
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extensions ['python_setup_py']
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115640) by extension 'python_setup_py'
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['ignore', 'ignore_ament_install']
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'ignore'
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'ignore_ament_install'
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['colcon_pkg']
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'colcon_pkg'
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['colcon_meta']
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'colcon_meta'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['ros']
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'ros'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['cmake', 'python']
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'cmake'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'python'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extensions ['python_setup_py']
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_115900) by extension 'python_setup_py'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['ignore', 'ignore_ament_install']
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'ignore'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'ignore_ament_install'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['colcon_pkg']
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'colcon_pkg'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['colcon_meta']
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'colcon_meta'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['ros']
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'ros'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['cmake', 'python']
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'cmake'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'python'
[1.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extensions ['python_setup_py']
[1.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120703) by extension 'python_setup_py'
[1.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['ignore', 'ignore_ament_install']
[1.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'ignore'
[1.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'ignore_ament_install'
[1.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['colcon_pkg']
[1.205s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'colcon_pkg'
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['colcon_meta']
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'colcon_meta'
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['ros']
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'ros'
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['cmake', 'python']
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'cmake'
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'python'
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extensions ['python_setup_py']
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_120846) by extension 'python_setup_py'
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['ignore', 'ignore_ament_install']
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'ignore'
[1.206s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'ignore_ament_install'
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['colcon_pkg']
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'colcon_pkg'
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['colcon_meta']
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'colcon_meta'
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['ros']
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'ros'
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['cmake', 'python']
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'cmake'
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'python'
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extensions ['python_setup_py']
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_121009) by extension 'python_setup_py'
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['ignore', 'ignore_ament_install']
[1.207s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'ignore'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'ignore_ament_install'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['colcon_pkg']
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'colcon_pkg'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['colcon_meta']
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'colcon_meta'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['ros']
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'ros'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['cmake', 'python']
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'cmake'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'python'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extensions ['python_setup_py']
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125028) by extension 'python_setup_py'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['ignore', 'ignore_ament_install']
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'ignore'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'ignore_ament_install'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['colcon_pkg']
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'colcon_pkg'
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['colcon_meta']
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'colcon_meta'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['ros']
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'ros'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['cmake', 'python']
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'cmake'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'python'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extensions ['python_setup_py']
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_125306) by extension 'python_setup_py'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['ignore', 'ignore_ament_install']
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'ignore'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'ignore_ament_install'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['colcon_pkg']
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'colcon_pkg'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['colcon_meta']
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'colcon_meta'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['ros']
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'ros'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['cmake', 'python']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'cmake'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'python'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extensions ['python_setup_py']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_130140) by extension 'python_setup_py'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['ignore', 'ignore_ament_install']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'ignore'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'ignore_ament_install'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['colcon_pkg']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'colcon_pkg'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['colcon_meta']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'colcon_meta'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['ros']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'ros'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['cmake', 'python']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'cmake'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'python'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extensions ['python_setup_py']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141111) by extension 'python_setup_py'
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['ignore', 'ignore_ament_install']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'ignore'
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'ignore_ament_install'
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['colcon_pkg']
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'colcon_pkg'
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['colcon_meta']
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'colcon_meta'
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['ros']
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'ros'
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['cmake', 'python']
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'cmake'
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'python'
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extensions ['python_setup_py']
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_141432) by extension 'python_setup_py'
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['ignore', 'ignore_ament_install']
[1.211s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'ignore'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'ignore_ament_install'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['colcon_pkg']
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'colcon_pkg'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['colcon_meta']
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'colcon_meta'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['ros']
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'ros'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['cmake', 'python']
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'cmake'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'python'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extensions ['python_setup_py']
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_145537) by extension 'python_setup_py'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['ignore', 'ignore_ament_install']
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'ignore'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'ignore_ament_install'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['colcon_pkg']
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'colcon_pkg'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['colcon_meta']
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'colcon_meta'
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['ros']
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'ros'
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['cmake', 'python']
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'cmake'
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'python'
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extensions ['python_setup_py']
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155038) by extension 'python_setup_py'
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['ignore', 'ignore_ament_install']
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'ignore'
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'ignore_ament_install'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['colcon_pkg']
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'colcon_pkg'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['colcon_meta']
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'colcon_meta'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['ros']
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'ros'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['cmake', 'python']
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'cmake'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'python'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extensions ['python_setup_py']
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155434) by extension 'python_setup_py'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['ignore', 'ignore_ament_install']
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'ignore'
[1.215s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'ignore_ament_install'
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['colcon_pkg']
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'colcon_pkg'
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['colcon_meta']
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'colcon_meta'
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['ros']
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'ros'
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['cmake', 'python']
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'cmake'
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'python'
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extensions ['python_setup_py']
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155614) by extension 'python_setup_py'
[1.216s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['ignore', 'ignore_ament_install']
[1.217s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'ignore'
[1.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'ignore_ament_install'
[1.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['colcon_pkg']
[1.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'colcon_pkg'
[1.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['colcon_meta']
[1.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'colcon_meta'
[1.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['ros']
[1.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'ros'
[1.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['cmake', 'python']
[1.218s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'cmake'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'python'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extensions ['python_setup_py']
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_155943) by extension 'python_setup_py'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['ignore', 'ignore_ament_install']
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'ignore'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'ignore_ament_install'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['colcon_pkg']
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'colcon_pkg'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['colcon_meta']
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'colcon_meta'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['ros']
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'ros'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['cmake', 'python']
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'cmake'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'python'
[1.219s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extensions ['python_setup_py']
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195338) by extension 'python_setup_py'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['ignore', 'ignore_ament_install']
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'ignore'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'ignore_ament_install'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['colcon_pkg']
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'colcon_pkg'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['colcon_meta']
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'colcon_meta'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['ros']
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'ros'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['cmake', 'python']
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'cmake'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'python'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extensions ['python_setup_py']
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195530) by extension 'python_setup_py'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['ignore', 'ignore_ament_install']
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'ignore'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'ignore_ament_install'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['colcon_pkg']
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'colcon_pkg'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['colcon_meta']
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'colcon_meta'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['ros']
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'ros'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['cmake', 'python']
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'cmake'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'python'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extensions ['python_setup_py']
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_195811) by extension 'python_setup_py'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['ignore', 'ignore_ament_install']
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'ignore'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'ignore_ament_install'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['colcon_pkg']
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'colcon_pkg'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['colcon_meta']
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'colcon_meta'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['ros']
[1.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'ros'
[1.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['cmake', 'python']
[1.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'cmake'
[1.222s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'python'
[1.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extensions ['python_setup_py']
[1.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202325) by extension 'python_setup_py'
[1.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['ignore', 'ignore_ament_install']
[1.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'ignore'
[1.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'ignore_ament_install'
[1.223s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['colcon_pkg']
[1.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'colcon_pkg'
[1.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['colcon_meta']
[1.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'colcon_meta'
[1.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['ros']
[1.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'ros'
[1.224s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['cmake', 'python']
[1.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'cmake'
[1.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'python'
[1.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extensions ['python_setup_py']
[1.225s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_202537) by extension 'python_setup_py'
[1.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['ignore', 'ignore_ament_install']
[1.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'ignore'
[1.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'ignore_ament_install'
[1.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['colcon_pkg']
[1.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'colcon_pkg'
[1.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['colcon_meta']
[1.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'colcon_meta'
[1.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['ros']
[1.226s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'ros'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['cmake', 'python']
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'cmake'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'python'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extensions ['python_setup_py']
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250712_203021) by extension 'python_setup_py'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['ignore', 'ignore_ament_install']
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'ignore'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'ignore_ament_install'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['colcon_pkg']
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'colcon_pkg'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['colcon_meta']
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'colcon_meta'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['ros']
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'ros'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['cmake', 'python']
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'cmake'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'python'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extensions ['python_setup_py']
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142017) by extension 'python_setup_py'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['ignore', 'ignore_ament_install']
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'ignore'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'ignore_ament_install'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['colcon_pkg']
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'colcon_pkg'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['colcon_meta']
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'colcon_meta'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['ros']
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'ros'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['cmake', 'python']
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'cmake'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'python'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extensions ['python_setup_py']
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142303) by extension 'python_setup_py'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['ignore', 'ignore_ament_install']
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'ignore'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'ignore_ament_install'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['colcon_pkg']
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'colcon_pkg'
[1.228s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['colcon_meta']
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'colcon_meta'
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['ros']
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'ros'
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['cmake', 'python']
[1.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'cmake'
[1.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'python'
[1.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extensions ['python_setup_py']
[1.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142549) by extension 'python_setup_py'
[1.232s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['ignore', 'ignore_ament_install']
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'ignore'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'ignore_ament_install'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['colcon_pkg']
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'colcon_pkg'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['colcon_meta']
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'colcon_meta'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['ros']
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'ros'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['cmake', 'python']
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'cmake'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'python'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extensions ['python_setup_py']
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_142932) by extension 'python_setup_py'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['ignore', 'ignore_ament_install']
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'ignore'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'ignore_ament_install'
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['colcon_pkg']
[1.233s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'colcon_pkg'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['colcon_meta']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'colcon_meta'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['ros']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'ros'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['cmake', 'python']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'cmake'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'python'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extensions ['python_setup_py']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_143633) by extension 'python_setup_py'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['ignore', 'ignore_ament_install']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'ignore'
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'ignore_ament_install'
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['colcon_pkg']
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'colcon_pkg'
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['colcon_meta']
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'colcon_meta'
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['ros']
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'ros'
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['cmake', 'python']
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'cmake'
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'python'
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extensions ['python_setup_py']
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144240) by extension 'python_setup_py'
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['ignore', 'ignore_ament_install']
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'ignore'
[1.235s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'ignore_ament_install'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['colcon_pkg']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'colcon_pkg'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['colcon_meta']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'colcon_meta'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['ros']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'ros'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['cmake', 'python']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'cmake'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'python'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extensions ['python_setup_py']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144422) by extension 'python_setup_py'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['ignore', 'ignore_ament_install']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'ignore'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'ignore_ament_install'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['colcon_pkg']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'colcon_pkg'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['colcon_meta']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'colcon_meta'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['ros']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'ros'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['cmake', 'python']
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'cmake'
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'python'
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extensions ['python_setup_py']
[1.237s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144621) by extension 'python_setup_py'
[1.241s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['ignore', 'ignore_ament_install']
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'ignore'
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'ignore_ament_install'
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['colcon_pkg']
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'colcon_pkg'
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['colcon_meta']
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'colcon_meta'
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['ros']
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'ros'
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['cmake', 'python']
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'cmake'
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'python'
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extensions ['python_setup_py']
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_144757) by extension 'python_setup_py'
[1.242s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['ignore', 'ignore_ament_install']
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'ignore'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'ignore_ament_install'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['colcon_pkg']
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'colcon_pkg'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['colcon_meta']
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'colcon_meta'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['ros']
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'ros'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['cmake', 'python']
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'cmake'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'python'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extensions ['python_setup_py']
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145022) by extension 'python_setup_py'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['ignore', 'ignore_ament_install']
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'ignore'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'ignore_ament_install'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['colcon_pkg']
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'colcon_pkg'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['colcon_meta']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'colcon_meta'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['ros']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'ros'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['cmake', 'python']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'cmake'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'python'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extensions ['python_setup_py']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145255) by extension 'python_setup_py'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['ignore', 'ignore_ament_install']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'ignore'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'ignore_ament_install'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['colcon_pkg']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'colcon_pkg'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['colcon_meta']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'colcon_meta'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['ros']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'ros'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['cmake', 'python']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'cmake'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'python'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extensions ['python_setup_py']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_145502) by extension 'python_setup_py'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['ignore', 'ignore_ament_install']
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'ignore'
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'ignore_ament_install'
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['colcon_pkg']
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'colcon_pkg'
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['colcon_meta']
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'colcon_meta'
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['ros']
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'ros'
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['cmake', 'python']
[1.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'cmake'
[1.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'python'
[1.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extensions ['python_setup_py']
[1.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150127) by extension 'python_setup_py'
[1.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['ignore', 'ignore_ament_install']
[1.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'ignore'
[1.246s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'ignore_ament_install'
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['colcon_pkg']
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'colcon_pkg'
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['colcon_meta']
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'colcon_meta'
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['ros']
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'ros'
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['cmake', 'python']
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'cmake'
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'python'
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extensions ['python_setup_py']
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150303) by extension 'python_setup_py'
[1.247s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['ignore', 'ignore_ament_install']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'ignore'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'ignore_ament_install'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['colcon_pkg']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'colcon_pkg'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['colcon_meta']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'colcon_meta'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['ros']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'ros'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['cmake', 'python']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'cmake'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'python'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extensions ['python_setup_py']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_150930) by extension 'python_setup_py'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['ignore', 'ignore_ament_install']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'ignore'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'ignore_ament_install'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['colcon_pkg']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'colcon_pkg'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['colcon_meta']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'colcon_meta'
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['ros']
[1.248s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'ros'
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['cmake', 'python']
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'cmake'
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'python'
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extensions ['python_setup_py']
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_151410) by extension 'python_setup_py'
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['ignore', 'ignore_ament_install']
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'ignore'
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'ignore_ament_install'
[1.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['colcon_pkg']
[1.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'colcon_pkg'
[1.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['colcon_meta']
[1.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'colcon_meta'
[1.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['ros']
[1.250s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'ros'
[1.251s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['cmake', 'python']
[1.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'cmake'
[1.252s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'python'
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extensions ['python_setup_py']
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152428) by extension 'python_setup_py'
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['ignore', 'ignore_ament_install']
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'ignore'
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'ignore_ament_install'
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['colcon_pkg']
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'colcon_pkg'
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['colcon_meta']
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'colcon_meta'
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['ros']
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'ros'
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['cmake', 'python']
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'cmake'
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'python'
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extensions ['python_setup_py']
[1.253s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_152705) by extension 'python_setup_py'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['ignore', 'ignore_ament_install']
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'ignore'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'ignore_ament_install'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['colcon_pkg']
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'colcon_pkg'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['colcon_meta']
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'colcon_meta'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['ros']
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'ros'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['cmake', 'python']
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'cmake'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'python'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extensions ['python_setup_py']
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153026) by extension 'python_setup_py'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['ignore', 'ignore_ament_install']
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'ignore'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'ignore_ament_install'
[1.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['colcon_pkg']
[1.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'colcon_pkg'
[1.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['colcon_meta']
[1.255s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'colcon_meta'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['ros']
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'ros'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['cmake', 'python']
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'cmake'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'python'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extensions ['python_setup_py']
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_153227) by extension 'python_setup_py'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['ignore', 'ignore_ament_install']
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'ignore'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'ignore_ament_install'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['colcon_pkg']
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'colcon_pkg'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['colcon_meta']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'colcon_meta'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['ros']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'ros'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['cmake', 'python']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'cmake'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'python'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extensions ['python_setup_py']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_154511) by extension 'python_setup_py'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['ignore', 'ignore_ament_install']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'ignore'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'ignore_ament_install'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['colcon_pkg']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'colcon_pkg'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['colcon_meta']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'colcon_meta'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['ros']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'ros'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['cmake', 'python']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'cmake'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'python'
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extensions ['python_setup_py']
[1.257s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250713_160357) by extension 'python_setup_py'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['ignore', 'ignore_ament_install']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'ignore'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'ignore_ament_install'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['colcon_pkg']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'colcon_pkg'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['colcon_meta']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'colcon_meta'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['ros']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'ros'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['cmake', 'python']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'cmake'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'python'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extensions ['python_setup_py']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163340) by extension 'python_setup_py'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['ignore', 'ignore_ament_install']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'ignore'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'ignore_ament_install'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['colcon_pkg']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'colcon_pkg'
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['colcon_meta']
[1.258s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'colcon_meta'
[1.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['ros']
[1.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'ros'
[1.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['cmake', 'python']
[1.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'cmake'
[1.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'python'
[1.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extensions ['python_setup_py']
[1.259s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_163802) by extension 'python_setup_py'
[1.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['ignore', 'ignore_ament_install']
[1.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'ignore'
[1.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'ignore_ament_install'
[1.260s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['colcon_pkg']
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'colcon_pkg'
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['colcon_meta']
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'colcon_meta'
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['ros']
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'ros'
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['cmake', 'python']
[1.262s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'cmake'
[1.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'python'
[1.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extensions ['python_setup_py']
[1.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164226) by extension 'python_setup_py'
[1.263s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['ignore', 'ignore_ament_install']
[1.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'ignore'
[1.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'ignore_ament_install'
[1.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['colcon_pkg']
[1.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'colcon_pkg'
[1.264s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['colcon_meta']
[1.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'colcon_meta'
[1.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['ros']
[1.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'ros'
[1.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['cmake', 'python']
[1.265s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'cmake'
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'python'
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extensions ['python_setup_py']
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_164907) by extension 'python_setup_py'
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['ignore', 'ignore_ament_install']
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'ignore'
[1.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'ignore_ament_install'
[1.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['colcon_pkg']
[1.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'colcon_pkg'
[1.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['colcon_meta']
[1.267s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'colcon_meta'
[1.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['ros']
[1.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'ros'
[1.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['cmake', 'python']
[1.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'cmake'
[1.268s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'python'
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extensions ['python_setup_py']
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_165931) by extension 'python_setup_py'
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['ignore', 'ignore_ament_install']
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'ignore'
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'ignore_ament_install'
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['colcon_pkg']
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'colcon_pkg'
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['colcon_meta']
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'colcon_meta'
[1.269s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['ros']
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'ros'
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['cmake', 'python']
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'cmake'
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'python'
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extensions ['python_setup_py']
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_174919) by extension 'python_setup_py'
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['ignore', 'ignore_ament_install']
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'ignore'
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'ignore_ament_install'
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['colcon_pkg']
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'colcon_pkg'
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['colcon_meta']
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'colcon_meta'
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['ros']
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'ros'
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['cmake', 'python']
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'cmake'
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'python'
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extensions ['python_setup_py']
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_231815) by extension 'python_setup_py'
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['ignore', 'ignore_ament_install']
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'ignore'
[1.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'ignore_ament_install'
[1.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['colcon_pkg']
[1.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'colcon_pkg'
[1.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['colcon_meta']
[1.272s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'colcon_meta'
[1.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['ros']
[1.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'ros'
[1.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['cmake', 'python']
[1.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'cmake'
[1.273s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'python'
[1.274s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extensions ['python_setup_py']
[1.274s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_232006) by extension 'python_setup_py'
[1.274s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['ignore', 'ignore_ament_install']
[1.274s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'ignore'
[1.274s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'ignore_ament_install'
[1.274s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['colcon_pkg']
[1.274s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'colcon_pkg'
[1.274s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['colcon_meta']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'colcon_meta'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['ros']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'ros'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['cmake', 'python']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'cmake'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'python'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extensions ['python_setup_py']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233120) by extension 'python_setup_py'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['ignore', 'ignore_ament_install']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'ignore'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'ignore_ament_install'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['colcon_pkg']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'colcon_pkg'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['colcon_meta']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'colcon_meta'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['ros']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'ros'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['cmake', 'python']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'cmake'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'python'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extensions ['python_setup_py']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233940) by extension 'python_setup_py'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['ignore', 'ignore_ament_install']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'ignore'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'ignore_ament_install'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['colcon_pkg']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'colcon_pkg'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['colcon_meta']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'colcon_meta'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['ros']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'ros'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['cmake', 'python']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'cmake'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'python'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extensions ['python_setup_py']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_233950) by extension 'python_setup_py'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['ignore', 'ignore_ament_install']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'ignore'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'ignore_ament_install'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['colcon_pkg']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'colcon_pkg'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['colcon_meta']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'colcon_meta'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['ros']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'ros'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['cmake', 'python']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'cmake'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'python'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extensions ['python_setup_py']
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250731_234113) by extension 'python_setup_py'
[1.277s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['ignore', 'ignore_ament_install']
[1.278s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'ignore'
[1.278s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'ignore_ament_install'
[1.278s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['colcon_pkg']
[1.278s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'colcon_pkg'
[1.278s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['colcon_meta']
[1.278s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'colcon_meta'
[1.279s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['ros']
[1.279s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'ros'
[1.279s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['cmake', 'python']
[1.279s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'cmake'
[1.279s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'python'
[1.279s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extensions ['python_setup_py']
[1.280s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_002041) by extension 'python_setup_py'
[1.280s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['ignore', 'ignore_ament_install']
[1.280s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'ignore'
[1.280s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'ignore_ament_install'
[1.280s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['colcon_pkg']
[1.280s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'colcon_pkg'
[1.280s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['colcon_meta']
[1.280s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'colcon_meta'
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['ros']
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'ros'
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['cmake', 'python']
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'cmake'
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'python'
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extensions ['python_setup_py']
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_003140) by extension 'python_setup_py'
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['ignore', 'ignore_ament_install']
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'ignore'
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'ignore_ament_install'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['colcon_pkg']
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'colcon_pkg'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['colcon_meta']
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'colcon_meta'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['ros']
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'ros'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['cmake', 'python']
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'cmake'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'python'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extensions ['python_setup_py']
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_005011) by extension 'python_setup_py'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['ignore', 'ignore_ament_install']
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'ignore'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'ignore_ament_install'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['colcon_pkg']
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'colcon_pkg'
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['colcon_meta']
[1.282s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'colcon_meta'
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['ros']
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'ros'
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['cmake', 'python']
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'cmake'
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'python'
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extensions ['python_setup_py']
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_132319) by extension 'python_setup_py'
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['ignore', 'ignore_ament_install']
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'ignore'
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'ignore_ament_install'
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['colcon_pkg']
[1.284s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'colcon_pkg'
[1.284s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['colcon_meta']
[1.284s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'colcon_meta'
[1.284s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['ros']
[1.284s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'ros'
[1.284s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['cmake', 'python']
[1.285s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'cmake'
[1.285s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'python'
[1.285s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extensions ['python_setup_py']
[1.285s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_135359) by extension 'python_setup_py'
[1.285s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['ignore', 'ignore_ament_install']
[1.285s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'ignore'
[1.285s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'ignore_ament_install'
[1.285s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['colcon_pkg']
[1.285s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'colcon_pkg'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['colcon_meta']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'colcon_meta'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['ros']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'ros'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['cmake', 'python']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'cmake'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'python'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extensions ['python_setup_py']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_140208) by extension 'python_setup_py'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['ignore', 'ignore_ament_install']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'ignore'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'ignore_ament_install'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['colcon_pkg']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'colcon_pkg'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['colcon_meta']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'colcon_meta'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['ros']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'ros'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['cmake', 'python']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'cmake'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'python'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extensions ['python_setup_py']
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_141541) by extension 'python_setup_py'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['ignore', 'ignore_ament_install']
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'ignore'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'ignore_ament_install'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['colcon_pkg']
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'colcon_pkg'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['colcon_meta']
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'colcon_meta'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['ros']
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'ros'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['cmake', 'python']
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'cmake'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'python'
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extensions ['python_setup_py']
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_143457) by extension 'python_setup_py'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['ignore', 'ignore_ament_install']
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'ignore'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'ignore_ament_install'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['colcon_pkg']
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'colcon_pkg'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['colcon_meta']
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'colcon_meta'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['ros']
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'ros'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['cmake', 'python']
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'cmake'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'python'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extensions ['python_setup_py']
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_144428) by extension 'python_setup_py'
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['ignore', 'ignore_ament_install']
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'ignore'
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'ignore_ament_install'
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['colcon_pkg']
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'colcon_pkg'
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['colcon_meta']
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'colcon_meta'
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['ros']
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'ros'
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['cmake', 'python']
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'cmake'
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'python'
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extensions ['python_setup_py']
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_160358) by extension 'python_setup_py'
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['ignore', 'ignore_ament_install']
[1.289s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'ignore'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'ignore_ament_install'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['colcon_pkg']
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'colcon_pkg'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['colcon_meta']
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'colcon_meta'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['ros']
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'ros'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['cmake', 'python']
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'cmake'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'python'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extensions ['python_setup_py']
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_161319) by extension 'python_setup_py'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['ignore', 'ignore_ament_install']
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'ignore'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'ignore_ament_install'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['colcon_pkg']
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'colcon_pkg'
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['colcon_meta']
[1.290s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'colcon_meta'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['ros']
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'ros'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['cmake', 'python']
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'cmake'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'python'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extensions ['python_setup_py']
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164324) by extension 'python_setup_py'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['ignore', 'ignore_ament_install']
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'ignore'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'ignore_ament_install'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['colcon_pkg']
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'colcon_pkg'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['colcon_meta']
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'colcon_meta'
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['ros']
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'ros'
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['cmake', 'python']
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'cmake'
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'python'
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extensions ['python_setup_py']
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_164351) by extension 'python_setup_py'
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['ignore', 'ignore_ament_install']
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'ignore'
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'ignore_ament_install'
[1.293s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['colcon_pkg']
[1.294s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'colcon_pkg'
[1.294s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['colcon_meta']
[1.294s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'colcon_meta'
[1.294s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['ros']
[1.294s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'ros'
[1.295s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['cmake', 'python']
[1.295s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'cmake'
[1.295s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'python'
[1.295s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extensions ['python_setup_py']
[1.295s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_170357) by extension 'python_setup_py'
[1.295s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['ignore', 'ignore_ament_install']
[1.295s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'ignore'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'ignore_ament_install'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['colcon_pkg']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'colcon_pkg'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['colcon_meta']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'colcon_meta'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['ros']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'ros'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['cmake', 'python']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'cmake'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'python'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extensions ['python_setup_py']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174024) by extension 'python_setup_py'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['ignore', 'ignore_ament_install']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'ignore'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'ignore_ament_install'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['colcon_pkg']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'colcon_pkg'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['colcon_meta']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'colcon_meta'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['ros']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'ros'
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['cmake', 'python']
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'cmake'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'python'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extensions ['python_setup_py']
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_174919) by extension 'python_setup_py'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['ignore', 'ignore_ament_install']
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'ignore'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'ignore_ament_install'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['colcon_pkg']
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'colcon_pkg'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['colcon_meta']
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'colcon_meta'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['ros']
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'ros'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['cmake', 'python']
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'cmake'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'python'
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extensions ['python_setup_py']
[1.297s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181027) by extension 'python_setup_py'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['ignore', 'ignore_ament_install']
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'ignore'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'ignore_ament_install'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['colcon_pkg']
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'colcon_pkg'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['colcon_meta']
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'colcon_meta'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['ros']
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'ros'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['cmake', 'python']
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'cmake'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'python'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extensions ['python_setup_py']
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_181836) by extension 'python_setup_py'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['ignore', 'ignore_ament_install']
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'ignore'
[1.298s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'ignore_ament_install'
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['colcon_pkg']
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'colcon_pkg'
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['colcon_meta']
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'colcon_meta'
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['ros']
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'ros'
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['cmake', 'python']
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'cmake'
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'python'
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extensions ['python_setup_py']
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_183307) by extension 'python_setup_py'
[1.300s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['ignore', 'ignore_ament_install']
[1.300s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'ignore'
[1.300s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'ignore_ament_install'
[1.300s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['colcon_pkg']
[1.301s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'colcon_pkg'
[1.301s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['colcon_meta']
[1.301s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'colcon_meta'
[1.301s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['ros']
[1.301s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'ros'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['cmake', 'python']
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'cmake'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'python'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extensions ['python_setup_py']
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_184823) by extension 'python_setup_py'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['ignore', 'ignore_ament_install']
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'ignore'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'ignore_ament_install'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['colcon_pkg']
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'colcon_pkg'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['colcon_meta']
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'colcon_meta'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['ros']
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'ros'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['cmake', 'python']
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'cmake'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'python'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extensions ['python_setup_py']
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_190721) by extension 'python_setup_py'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['ignore', 'ignore_ament_install']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'ignore'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'ignore_ament_install'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['colcon_pkg']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'colcon_pkg'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['colcon_meta']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'colcon_meta'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['ros']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'ros'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['cmake', 'python']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'cmake'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'python'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extensions ['python_setup_py']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_192602) by extension 'python_setup_py'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['ignore', 'ignore_ament_install']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'ignore'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'ignore_ament_install'
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['colcon_pkg']
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'colcon_pkg'
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['colcon_meta']
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'colcon_meta'
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['ros']
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'ros'
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['cmake', 'python']
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'cmake'
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'python'
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extensions ['python_setup_py']
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(usv_session_20250801_203423) by extension 'python_setup_py'
[1.304s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.304s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.304s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.304s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.304s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.371s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.371s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.377s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 271 installed packages in /opt/ros/humble
[1.380s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[1.457s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_args' from command line to 'None'
[1.457s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target' from command line to 'None'
[1.457s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.457s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_cache' from command line to 'False'
[1.458s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_clean_first' from command line to 'False'
[1.458s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'cmake_force_configure' from command line to 'False'
[1.458s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'ament_cmake_args' from command line to 'None'
[1.458s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_cmake_args' from command line to 'None'
[1.458s] Level 5:colcon.colcon_core.verb:set package 'xt_user' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.459s] DEBUG:colcon.colcon_core.verb:Building package 'xt_user' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user', 'merge_install': False, 'path': '/home/<USER>/project2_new（复件）/USVControl-user/ControlNode/planning', 'symlink_install': False, 'test_result_base': None}
[1.459s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.463s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.464s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/project2_new（复件）/USVControl-user/ControlNode/planning' with build type 'ament_cmake'
[1.464s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/project2_new（复件）/USVControl-user/ControlNode/planning'
[1.479s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.479s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.479s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.537s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new（复件）/USVControl-user/build/xt_user -- -j8 -l8
[4.731s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(xt_user)
[4.732s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user' returned '2': CMAKE_PREFIX_PATH=/opt/ros/humble /usr/bin/cmake --build /home/<USER>/project2_new（复件）/USVControl-user/build/xt_user -- -j8 -l8
[4.751s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user' for CMake module files
[4.751s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user' for CMake config files
[4.752s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'cmake_prefix_path')
[4.752s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.ps1'
[4.756s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.dsv'
[4.757s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/hook/cmake_prefix_path.sh'
[4.759s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/lib'
[4.760s] Level 1:colcon.colcon_core.shell:create_environment_hook('xt_user', 'ld_library_path_lib')
[4.760s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.ps1'
[4.762s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.dsv'
[4.763s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/hook/ld_library_path_lib.sh'
[4.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/bin'
[4.764s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/lib/pkgconfig/xt_user.pc'
[4.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/lib/python3.10/site-packages'
[4.765s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/bin'
[4.766s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/package.ps1'
[4.768s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/package.dsv'
[4.770s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/package.sh'
[4.773s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/package.bash'
[4.775s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/xt_user/package.zsh'
[4.777s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/project2_new（复件）/USVControl-user/install/xt_user/share/colcon-core/packages/xt_user)
[4.778s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[4.779s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[4.779s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '2'
[4.779s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[4.806s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[4.807s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[4.807s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[4.858s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[4.859s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/project2_new（复件）/USVControl-user/install/local_setup.ps1'
[4.861s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/project2_new（复件）/USVControl-user/install/_local_setup_util_ps1.py'
[4.863s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/project2_new（复件）/USVControl-user/install/setup.ps1'
[4.866s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/project2_new（复件）/USVControl-user/install/local_setup.sh'
[4.869s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/project2_new（复件）/USVControl-user/install/_local_setup_util_sh.py'
[4.871s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/project2_new（复件）/USVControl-user/install/setup.sh'
[4.873s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/project2_new（复件）/USVControl-user/install/local_setup.bash'
[4.875s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/project2_new（复件）/USVControl-user/install/setup.bash'
[4.877s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/project2_new（复件）/USVControl-user/install/local_setup.zsh'
[4.878s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/project2_new（复件）/USVControl-user/install/setup.zsh'
