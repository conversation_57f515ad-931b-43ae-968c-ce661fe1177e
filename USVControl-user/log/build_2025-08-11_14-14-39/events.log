[0.000000] (-) TimerEvent: {}
[0.002198] (xt_user) JobQueued: {'identifier': 'xt_user', 'dependencies': OrderedDict()}
[0.002908] (xt_user) JobStarted: {'identifier': 'xt_user'}
[0.050962] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'cmake'}
[0.058616] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'build'}
[0.058816] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/project2_new（复件）'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1612'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1753'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=5ce7623966f63025ba1a23456894c89d'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '237450'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:24796'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1753,unix/xumj-virtual-machine:/tmp/.ICE-unix/1753'), ('INVOCATION_ID', 'dfbaa205b2d649429eddca64e158508e'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.FTM3A3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2577d93802.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=5ce7623966f63025ba1a23456894c89d'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.098646] (-) TimerEvent: {}
[0.141939] (xt_user) StderrLine: {'line': b'\x1b[0mCMake Error: The current CMakeCache.txt directory /home/<USER>/project2_new\xef\xbc\x88\xe5\xa4\x8d\xe4\xbb\xb6\xef\xbc\x89/USVControl-user/build/xt_user/CMakeCache.txt is different than the directory /home/<USER>/project2_new/USVControl-user/build/xt_user where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt\x1b[0m\n'}
[0.198841] (-) TimerEvent: {}
[0.280270] (xt_user) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.299866] (-) TimerEvent: {}
[0.401975] (-) TimerEvent: {}
[0.502942] (-) TimerEvent: {}
[0.604718] (-) TimerEvent: {}
[0.697124] (xt_user) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.705660] (-) TimerEvent: {}
[0.806066] (-) TimerEvent: {}
[0.849350] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.857983] (xt_user) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.895921] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.907182] (-) TimerEvent: {}
[0.935228] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.005706] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[1.008443] (-) TimerEvent: {}
[1.110895] (-) TimerEvent: {}
[1.163897] (xt_user) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[1.183423] (xt_user) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[1.211281] (-) TimerEvent: {}
[1.313316] (-) TimerEvent: {}
[1.417423] (-) TimerEvent: {}
[1.452457] (xt_user) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[1.518466] (-) TimerEvent: {}
[1.625773] (-) TimerEvent: {}
[1.710759] (xt_user) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[1.727195] (-) TimerEvent: {}
[1.828734] (-) TimerEvent: {}
[1.929180] (-) TimerEvent: {}
[1.929715] (xt_user) StdoutLine: {'line': b'-- Configuring done\n'}
[1.970027] (xt_user) StdoutLine: {'line': b'-- Generating done\n'}
[1.990498] (xt_user) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/project2_new/USVControl-user/build/xt_user\n'}
[2.029742] (-) TimerEvent: {}
[2.065115] (xt_user) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target planning_hpp\x1b[0m\n'}
[2.081409] (xt_user) StdoutLine: {'line': b'[ 14%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o\x1b[0m\n'}
[2.130111] (-) TimerEvent: {}
[2.231586] (-) TimerEvent: {}
[2.332251] (-) TimerEvent: {}
[2.434198] (-) TimerEvent: {}
[2.538645] (-) TimerEvent: {}
[2.639241] (-) TimerEvent: {}
[2.741136] (-) TimerEvent: {}
[2.843914] (-) TimerEvent: {}
[2.944407] (-) TimerEvent: {}
[3.006752] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[KMotionPlan::MotionPlan()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.007426] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:36:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kvoid* memset(void*, int, size_t)\x1b[m\x1b[K\xe2\x80\x99 clearing an object of type \xe2\x80\x98\x1b[01m\x1b[Kstruct ObstacleAvoidanceParam\x1b[m\x1b[K\xe2\x80\x99 with no trivial copy-assignment; use assignment or value-initialization instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wclass-memaccess\x07-Wclass-memaccess\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.007846] (xt_user) StderrLine: {'line': b'   36 |         \x1b[01;35m\x1b[Kmemset(&obstacleAvoidance, 0, sizeof(ObstacleAvoidanceParam))\x1b[m\x1b[K;\n'}
[3.008031] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.008138] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[3.008212] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:50:1:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstruct ObstacleAvoidanceParam\x1b[m\x1b[K\xe2\x80\x99 declared here\n'}
[3.008479] (xt_user) StderrLine: {'line': b'   50 | \x1b[01;36m\x1b[K{\x1b[m\x1b[K\n'}
[3.008556] (xt_user) StderrLine: {'line': b'      | \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[3.010192] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.010326] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:261:43:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kboatstate\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.010397] (xt_user) StderrLine: {'line': b'  261 | void MotionPlan::TrajecotryGet(\x1b[01;35m\x1b[KTransState boatstate\x1b[m\x1b[K, anchor_mission_control task)\n'}
[3.010459] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;35m\x1b[K~~~~~~~~~~~^~~~~~~~~\x1b[m\x1b[K\n'}
[3.010585] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.010664] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:344:47:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[1]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.010733] (xt_user) StderrLine: {'line': b'  344 |         float pos[2] = { \x1b[01;35m\x1b[Kboatstate.PostoHome[1]\x1b[m\x1b[K, boatstate.PostoHome[0] }; // [X, Y] = [lon, lat]\n'}
[3.010796] (xt_user) StderrLine: {'line': b'      |                          \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[3.010888] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:344:71:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[0]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.010953] (xt_user) StderrLine: {'line': b'  344 |         float pos[2] = { boatstate.PostoHome[1], \x1b[01;35m\x1b[Kboatstate.PostoHome[0]\x1b[m\x1b[K }; // [X, Y] = [lon, lat]\n'}
[3.011009] (xt_user) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[3.012661] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:451:15:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kredeclaration of \xe2\x80\x98\x1b[01m\x1b[Kfloat p_end [2]\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.012783] (xt_user) StderrLine: {'line': b'  451 |         float \x1b[01;31m\x1b[Kp_end\x1b[m\x1b[K[2] = { tracking_task.mission[dotTrackingParam.TrackNum].lon,\n'}
[3.012845] (xt_user) StderrLine: {'line': b'      |               \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[3.013480] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:376:15:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kfloat p_end [2]\x1b[m\x1b[K\xe2\x80\x99 previously declared here\n'}
[3.013562] (xt_user) StderrLine: {'line': b'  376 |         float \x1b[01;36m\x1b[Kp_end\x1b[m\x1b[K[2] = {\n'}
[3.013623] (xt_user) StderrLine: {'line': b'      |               \x1b[01;36m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[3.013680] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:453:15:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kredeclaration of \xe2\x80\x98\x1b[01m\x1b[Kfloat distance_to_target\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.013738] (xt_user) StderrLine: {'line': b'  453 |         float \x1b[01;31m\x1b[Kdistance_to_target\x1b[m\x1b[K = sqrt((p_end[0] - pos[0])*(p_end[0] - pos[0]) +\n'}
[3.013872] (xt_user) StderrLine: {'line': b'      |               \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.013930] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:380:15:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kfloat distance_to_target\x1b[m\x1b[K\xe2\x80\x99 previously declared here\n'}
[3.013989] (xt_user) StderrLine: {'line': b'  380 |         float \x1b[01;36m\x1b[Kdistance_to_target\x1b[m\x1b[K = sqrtf(powf(p_end[0] - pos[0], 2) + powf(p_end[1] - pos[1], 2));\n'}
[3.014212] (xt_user) StderrLine: {'line': b'      |               \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.014276] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:460:15:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kredeclaration of \xe2\x80\x98\x1b[01m\x1b[Kfloat current_switch_distance\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.014333] (xt_user) StderrLine: {'line': b'  460 |         float \x1b[01;31m\x1b[Kcurrent_switch_distance\x1b[m\x1b[K = 50.0f; // \xe9\xbb\x98\xe8\xae\xa450\xe7\xb1\xb3\xe5\x88\x87\xe6\x8d\xa2\xe8\xb7\x9d\xe7\xa6\xbb\n'}
[3.014390] (xt_user) StderrLine: {'line': b'      |               \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.014453] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:383:15:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kfloat current_switch_distance\x1b[m\x1b[K\xe2\x80\x99 previously declared here\n'}
[3.014511] (xt_user) StderrLine: {'line': b'  383 |         float \x1b[01;36m\x1b[Kcurrent_switch_distance\x1b[m\x1b[K = (currentPhase == PHASE_TRACK_ACCURACY) ? 5.0f : dotTrackingParam.switchlen;\n'}
[3.014567] (xt_user) StderrLine: {'line': b'      |               \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.027675] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:471:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kt\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope; did you mean \xe2\x80\x98\x1b[01m\x1b[Ktm\x1b[m\x1b[K\xe2\x80\x99?\n'}
[3.027908] (xt_user) StderrLine: {'line': b'  471 |                             float check_y = pos[1] + \x1b[01;31m\x1b[Kt\x1b[m\x1b[K * dy;\n'}
[3.027974] (xt_user) StderrLine: {'line': b'      |                                                      \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[3.028051] (xt_user) StderrLine: {'line': b'      |                                                      \x1b[32m\x1b[Ktm\x1b[m\x1b[K\n'}
[3.035540] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:471:58:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kdy\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[3.035870] (xt_user) StderrLine: {'line': b'  471 |                             float check_y = pos[1] + t * \x1b[01;31m\x1b[Kdy\x1b[m\x1b[K;\n'}
[3.035954] (xt_user) StderrLine: {'line': b'      |                                                          \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[3.038952] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:476:62:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcheck_x\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope; did you mean \xe2\x80\x98\x1b[01m\x1b[Kcheck_y\x1b[m\x1b[K\xe2\x80\x99?\n'}
[3.039161] (xt_user) StderrLine: {'line': b'  476 |                                     if (IsObstacleAtPosition(\x1b[01;31m\x1b[Kcheck_x\x1b[m\x1b[K + ox, check_y + oy)) {\n'}
[3.039241] (xt_user) StderrLine: {'line': b'      |                                                              \x1b[01;31m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[3.039301] (xt_user) StderrLine: {'line': b'      |                                                              \x1b[32m\x1b[Kcheck_y\x1b[m\x1b[K\n'}
[3.045210] (-) TimerEvent: {}
[3.048369] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:477:41:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kdirect_path_safe\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[3.048567] (xt_user) StderrLine: {'line': b'  477 |                                         \x1b[01;31m\x1b[Kdirect_path_safe\x1b[m\x1b[K = false;\n'}
[3.048630] (xt_user) StderrLine: {'line': b'      |                                         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.061430] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:482:38:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kdirect_path_safe\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[3.061678] (xt_user) StderrLine: {'line': b'  482 |                                 if (!\x1b[01;31m\x1b[Kdirect_path_safe\x1b[m\x1b[K) break;\n'}
[3.061790] (xt_user) StderrLine: {'line': b'      |                                      \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.070626] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:484:34:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kdirect_path_safe\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[3.070915] (xt_user) StderrLine: {'line': b'  484 |                             if (!\x1b[01;31m\x1b[Kdirect_path_safe\x1b[m\x1b[K) break;\n'}
[3.071038] (xt_user) StderrLine: {'line': b'      |                                  \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.071115] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:484:52:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kbreak statement not within loop or switch\n'}
[3.071185] (xt_user) StderrLine: {'line': b'  484 |                             if (!direct_path_safe) \x1b[01;31m\x1b[Kbreak\x1b[m\x1b[K;\n'}
[3.071255] (xt_user) StderrLine: {'line': b'      |                                                    \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[3.071322] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:380:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdistance_to_target\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.071392] (xt_user) StderrLine: {'line': b'  380 |         float \x1b[01;35m\x1b[Kdistance_to_target\x1b[m\x1b[K = sqrtf(powf(p_end[0] - pos[0], 2) + powf(p_end[1] - pos[1], 2));\n'}
[3.071458] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.071521] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:383:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kcurrent_switch_distance\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.071589] (xt_user) StderrLine: {'line': b'  383 |         float \x1b[01;35m\x1b[Kcurrent_switch_distance\x1b[m\x1b[K = (currentPhase == PHASE_TRACK_ACCURACY) ? 5.0f : dotTrackingParam.switchlen;\n'}
[3.071657] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.071982] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K At global scope:\n'}
[3.072111] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:487:17:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[3.072186] (xt_user) StderrLine: {'line': b'  487 |                 \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[3.072249] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[3.072312] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:490:17:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kif\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.072369] (xt_user) StderrLine: {'line': b'  490 |                 \x1b[01;31m\x1b[Kif\x1b[m\x1b[K (direct_path_safe && !emergency_cheat_mode && dotTrackingParam.TrackNum < 2) {\n'}
[3.072425] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[3.072480] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:495:19:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kelse\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.072540] (xt_user) StderrLine: {'line': b'  495 |                 } \x1b[01;31m\x1b[Kelse\x1b[m\x1b[K {\n'}
[3.072596] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[3.072650] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:624:13:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[3.072707] (xt_user) StderrLine: {'line': b'  624 |             \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[3.072762] (xt_user) StderrLine: {'line': b'      |             \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[3.072817] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:625:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[3.072976] (xt_user) StderrLine: {'line': b'  625 |         \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[3.073037] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[3.073094] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:628:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kif\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.073151] (xt_user) StderrLine: {'line': b'  628 |         \x1b[01;31m\x1b[Kif\x1b[m\x1b[K (distance_to_target <= current_switch_distance) {\n'}
[3.073207] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[3.073262] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:654:11:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kelse\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.073326] (xt_user) StderrLine: {'line': b'  654 |         } \x1b[01;31m\x1b[Kelse\x1b[m\x1b[K {\n'}
[3.073382] (xt_user) StderrLine: {'line': b'      |           \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[3.073437] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:664:5:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[3.073493] (xt_user) StderrLine: {'line': b'  664 |     \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[3.073548] (xt_user) StderrLine: {'line': b'      |     \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[3.073601] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:667:5:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kif\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.073657] (xt_user) StderrLine: {'line': b'  667 |     \x1b[01;31m\x1b[Kif\x1b[m\x1b[K (dotTrackingParam.TrackNum > phaseTransitionFlag) {\n'}
[3.073715] (xt_user) StderrLine: {'line': b'      |     \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[3.073885] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:713:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kif\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.074046] (xt_user) StderrLine: {'line': b'  713 |         \x1b[01;31m\x1b[Kif\x1b[m\x1b[K (currentPhase == PHASE_HEADING_CONTROL) {\n'}
[3.074115] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[3.074177] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:715:11:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kelse\x1b[m\x1b[K\xe2\x80\x99\n'}
[3.074247] (xt_user) StderrLine: {'line': b'  715 |         } \x1b[01;31m\x1b[Kelse\x1b[m\x1b[K if (currentPhase == PHASE_TRACK_ACCURACY) {\n'}
[3.074310] (xt_user) StderrLine: {'line': b'      |           \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[3.074366] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:720:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KactionModelData\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[3.074497] (xt_user) StderrLine: {'line': b'  720 |         \x1b[01;31m\x1b[KactionModelData\x1b[m\x1b[K.model = 10;\n'}
[3.074556] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.074614] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:721:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KactionHeadvelControl\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[3.074672] (xt_user) StderrLine: {'line': b'  721 |         \x1b[01;31m\x1b[KactionHeadvelControl\x1b[m\x1b[K.model = 2;\n'}
[3.074729] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.076003] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:722:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KactionHeadvelControl\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[3.076108] (xt_user) StderrLine: {'line': b'  722 |         \x1b[01;31m\x1b[KactionHeadvelControl\x1b[m\x1b[K.heading = anglevel[0];\n'}
[3.076174] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.077322] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:723:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KactionHeadvelControl\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[3.077457] (xt_user) StderrLine: {'line': b'  723 |         \x1b[01;31m\x1b[KactionHeadvelControl\x1b[m\x1b[K.velocity = anglevel[1];\n'}
[3.077771] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.077910] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:724:1:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[3.077983] (xt_user) StderrLine: {'line': b'  724 | \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[3.078073] (xt_user) StderrLine: {'line': b'      | \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[3.093975] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::EvaluatePathSafety(float*, float, float*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.094383] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:978:87:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KtargetPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.094505] (xt_user) StderrLine: {'line': b'  978 | float MotionPlan::EvaluatePathSafety(float currentPos[2], float candidateAngle, \x1b[01;35m\x1b[Kfloat targetPos[2]\x1b[m\x1b[K)\n'}
[3.094573] (xt_user) StderrLine: {'line': b'      |                                                                                 \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.094643] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::GetObstacleDistance(float*, float*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.094720] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1038:45:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.094802] (xt_user) StderrLine: {'line': b' 1038 | float MotionPlan::GetObstacleDistance(\x1b[01;35m\x1b[Kfloat currentPos[2]\x1b[m\x1b[K, float targetPos[2])\n'}
[3.094878] (xt_user) StderrLine: {'line': b'      |                                       \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.094952] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1038:66:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KtargetPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.095029] (xt_user) StderrLine: {'line': b' 1038 | float MotionPlan::GetObstacleDistance(float currentPos[2], \x1b[01;35m\x1b[Kfloat targetPos[2]\x1b[m\x1b[K)\n'}
[3.095166] (xt_user) StderrLine: {'line': b'      |                                                            \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.095236] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool MotionPlan::CheckMissionFailure(float*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.095307] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1059:44:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.095386] (xt_user) StderrLine: {'line': b' 1059 | bool MotionPlan::CheckMissionFailure(\x1b[01;35m\x1b[Kfloat currentPos[2]\x1b[m\x1b[K)\n'}
[3.095501] (xt_user) StderrLine: {'line': b'      |                                      \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.097943] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::CalculateDetourAngle(float*, float*, float)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[3.098150] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1185:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[K(((double)(* start)) + (((double)2.0e+2f) * cos(((((double)left_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.099263] (xt_user) StderrLine: {'line': b' 1185 |                 \x1b[01;35m\x1b[Kstart[0] + 200.0f * cos(left_angle * M_PI / 180.0f)\x1b[m\x1b[K,\n'}
[3.100933] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.101030] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1186:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[K(((double)(*(start + 4))) + (((double)2.0e+2f) * sin(((((double)left_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.101103] (xt_user) StderrLine: {'line': b' 1186 |                 \x1b[01;35m\x1b[Kstart[1] + 200.0f * sin(left_angle * M_PI / 180.0f)\x1b[m\x1b[K\n'}
[3.101153] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.101210] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1192:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[K(((double)(* start)) + (((double)2.0e+2f) * cos(((((double)right_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.103479] (xt_user) StderrLine: {'line': b' 1192 |                 \x1b[01;35m\x1b[Kstart[0] + 200.0f * cos(right_angle * M_PI / 180.0f)\x1b[m\x1b[K,\n'}
[3.103555] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.103612] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1193:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[K(((double)(*(start + 4))) + (((double)2.0e+2f) * sin(((((double)right_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.103675] (xt_user) StderrLine: {'line': b' 1193 |                 \x1b[01;35m\x1b[Kstart[1] + 200.0f * sin(right_angle * M_PI / 180.0f)\x1b[m\x1b[K\n'}
[3.103804] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[3.103878] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1173:62:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[3.103962] (xt_user) StderrLine: {'line': b' 1173 | float MotionPlan::CalculateDetourAngle(float start[2], \x1b[01;35m\x1b[Kfloat end[2]\x1b[m\x1b[K, float direct_angle)\n'}
[3.104034] (xt_user) StderrLine: {'line': b'      |                                                        \x1b[01;35m\x1b[K~~~~~~^~~~~~\x1b[m\x1b[K\n'}
[3.146735] (-) TimerEvent: {}
[3.248166] (-) TimerEvent: {}
[3.261494] (xt_user) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/planning_hpp.dir/build.make:90\xef\xbc\x9aCMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o] \xe9\x94\x99\xe8\xaf\xaf 1\n'}
[3.262322] (xt_user) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:139\xef\xbc\x9aCMakeFiles/planning_hpp.dir/all] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[3.262728] (xt_user) StderrLine: {'line': b'gmake: *** [Makefile:146\xef\xbc\x9aall] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[3.268826] (xt_user) CommandEnded: {'returncode': 2}
[3.315431] (xt_user) JobEnded: {'identifier': 'xt_user', 'rc': 2}
[3.324404] (-) EventReactorShutdown: {}
