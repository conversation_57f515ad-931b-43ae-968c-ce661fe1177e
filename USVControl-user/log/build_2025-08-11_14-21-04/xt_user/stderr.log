[0mCMake Error: The current CMakeCache.txt directory /home/<USER>/project2_new（复件）/USVControl-user/build/xt_user/CMakeCache.txt is different than the directory /home/<USER>/project2_new/USVControl-user/build/xt_user where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt[0m
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In constructor ‘[01m[KMotionPlan::MotionPlan()[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:36:15:[m[K [01;35m[Kwarning: [m[K‘[01m[Kvoid* memset(void*, int, size_t)[m[K’ clearing an object of type ‘[01m[Kstruct ObstacleAvoidanceParam[m[K’ with no trivial copy-assignment; use assignment or value-initialization instead [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wclass-memaccess-Wclass-memaccess]8;;[m[K]
   36 |         [01;35m[Kmemset(&obstacleAvoidance, 0, sizeof(ObstacleAvoidanceParam))[m[K;
      |         [01;35m[K~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
In file included from [01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1[m[K:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:50:1:[m[K [01;36m[Knote: [m[K‘[01m[Kstruct ObstacleAvoidanceParam[m[K’ declared here
   50 | [01;36m[K{[m[K
      | [01;36m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:261:43:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kboatstate[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  261 | void MotionPlan::TrajecotryGet([01;35m[KTransState boatstate[m[K, anchor_mission_control task)
      |                                [01;35m[K~~~~~~~~~~~^~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:344:47:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[1][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
  344 |         float pos[2] = { [01;35m[Kboatstate.PostoHome[1][m[K, boatstate.PostoHome[0] }; // [X, Y] = [lon, lat]
      |                          [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:344:71:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[Kboatstate.TransState::PostoHome[0][m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
  344 |         float pos[2] = { boatstate.PostoHome[1], [01;35m[Kboatstate.PostoHome[0][m[K }; // [X, Y] = [lon, lat]
      |                                                  [01;35m[K~~~~~~~~~~~~~~~~~~~~~^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:451:15:[m[K [01;31m[Kerror: [m[Kredeclaration of ‘[01m[Kfloat p_end [2][m[K’
  451 |         float [01;31m[Kp_end[m[K[2] = { tracking_task.mission[dotTrackingParam.TrackNum].lon,
      |               [01;31m[K^~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:376:15:[m[K [01;36m[Knote: [m[K‘[01m[Kfloat p_end [2][m[K’ previously declared here
  376 |         float [01;36m[Kp_end[m[K[2] = {
      |               [01;36m[K^~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:453:15:[m[K [01;31m[Kerror: [m[Kredeclaration of ‘[01m[Kfloat distance_to_target[m[K’
  453 |         float [01;31m[Kdistance_to_target[m[K = sqrt((p_end[0] - pos[0])*(p_end[0] - pos[0]) +
      |               [01;31m[K^~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:380:15:[m[K [01;36m[Knote: [m[K‘[01m[Kfloat distance_to_target[m[K’ previously declared here
  380 |         float [01;36m[Kdistance_to_target[m[K = sqrtf(powf(p_end[0] - pos[0], 2) + powf(p_end[1] - pos[1], 2));
      |               [01;36m[K^~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:460:15:[m[K [01;31m[Kerror: [m[Kredeclaration of ‘[01m[Kfloat current_switch_distance[m[K’
  460 |         float [01;31m[Kcurrent_switch_distance[m[K = 50.0f; // 默认50米切换距离
      |               [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:383:15:[m[K [01;36m[Knote: [m[K‘[01m[Kfloat current_switch_distance[m[K’ previously declared here
  383 |         float [01;36m[Kcurrent_switch_distance[m[K = (currentPhase == PHASE_TRACK_ACCURACY) ? 5.0f : dotTrackingParam.switchlen;
      |               [01;36m[K^~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:471:54:[m[K [01;31m[Kerror: [m[K‘[01m[Kt[m[K’ was not declared in this scope; did you mean ‘[01m[Ktm[m[K’?
  471 |                             float check_y = pos[1] + [01;31m[Kt[m[K * dy;
      |                                                      [01;31m[K^[m[K
      |                                                      [32m[Ktm[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:471:58:[m[K [01;31m[Kerror: [m[K‘[01m[Kdy[m[K’ was not declared in this scope
  471 |                             float check_y = pos[1] + t * [01;31m[Kdy[m[K;
      |                                                          [01;31m[K^~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:476:62:[m[K [01;31m[Kerror: [m[K‘[01m[Kcheck_x[m[K’ was not declared in this scope; did you mean ‘[01m[Kcheck_y[m[K’?
  476 |                                     if (IsObstacleAtPosition([01;31m[Kcheck_x[m[K + ox, check_y + oy)) {
      |                                                              [01;31m[K^~~~~~~[m[K
      |                                                              [32m[Kcheck_y[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:477:41:[m[K [01;31m[Kerror: [m[K‘[01m[Kdirect_path_safe[m[K’ was not declared in this scope
  477 |                                         [01;31m[Kdirect_path_safe[m[K = false;
      |                                         [01;31m[K^~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:482:38:[m[K [01;31m[Kerror: [m[K‘[01m[Kdirect_path_safe[m[K’ was not declared in this scope
  482 |                                 if (![01;31m[Kdirect_path_safe[m[K) break;
      |                                      [01;31m[K^~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:484:34:[m[K [01;31m[Kerror: [m[K‘[01m[Kdirect_path_safe[m[K’ was not declared in this scope
  484 |                             if (![01;31m[Kdirect_path_safe[m[K) break;
      |                                  [01;31m[K^~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:484:52:[m[K [01;31m[Kerror: [m[Kbreak statement not within loop or switch
  484 |                             if (!direct_path_safe) [01;31m[Kbreak[m[K;
      |                                                    [01;31m[K^~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:380:15:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kdistance_to_target[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  380 |         float [01;35m[Kdistance_to_target[m[K = sqrtf(powf(p_end[0] - pos[0], 2) + powf(p_end[1] - pos[1], 2));
      |               [01;35m[K^~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:383:15:[m[K [01;35m[Kwarning: [m[Kunused variable ‘[01m[Kcurrent_switch_distance[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable-Wunused-variable]8;;[m[K]
  383 |         float [01;35m[Kcurrent_switch_distance[m[K = (currentPhase == PHASE_TRACK_ACCURACY) ? 5.0f : dotTrackingParam.switchlen;
      |               [01;35m[K^~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K At global scope:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:487:17:[m[K [01;31m[Kerror: [m[Kexpected declaration before ‘[01m[K}[m[K’ token
  487 |                 [01;31m[K}[m[K
      |                 [01;31m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:490:17:[m[K [01;31m[Kerror: [m[Kexpected unqualified-id before ‘[01m[Kif[m[K’
  490 |                 [01;31m[Kif[m[K (direct_path_safe && !emergency_cheat_mode && dotTrackingParam.TrackNum < 2) {
      |                 [01;31m[K^~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:495:19:[m[K [01;31m[Kerror: [m[Kexpected unqualified-id before ‘[01m[Kelse[m[K’
  495 |                 } [01;31m[Kelse[m[K {
      |                   [01;31m[K^~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:624:13:[m[K [01;31m[Kerror: [m[Kexpected declaration before ‘[01m[K}[m[K’ token
  624 |             [01;31m[K}[m[K
      |             [01;31m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:625:9:[m[K [01;31m[Kerror: [m[Kexpected declaration before ‘[01m[K}[m[K’ token
  625 |         [01;31m[K}[m[K
      |         [01;31m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:628:9:[m[K [01;31m[Kerror: [m[Kexpected unqualified-id before ‘[01m[Kif[m[K’
  628 |         [01;31m[Kif[m[K (distance_to_target <= current_switch_distance) {
      |         [01;31m[K^~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:654:11:[m[K [01;31m[Kerror: [m[Kexpected unqualified-id before ‘[01m[Kelse[m[K’
  654 |         } [01;31m[Kelse[m[K {
      |           [01;31m[K^~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:664:5:[m[K [01;31m[Kerror: [m[Kexpected declaration before ‘[01m[K}[m[K’ token
  664 |     [01;31m[K}[m[K
      |     [01;31m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:667:5:[m[K [01;31m[Kerror: [m[Kexpected unqualified-id before ‘[01m[Kif[m[K’
  667 |     [01;31m[Kif[m[K (dotTrackingParam.TrackNum > phaseTransitionFlag) {
      |     [01;31m[K^~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:713:9:[m[K [01;31m[Kerror: [m[Kexpected unqualified-id before ‘[01m[Kif[m[K’
  713 |         [01;31m[Kif[m[K (currentPhase == PHASE_HEADING_CONTROL) {
      |         [01;31m[K^~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:715:11:[m[K [01;31m[Kerror: [m[Kexpected unqualified-id before ‘[01m[Kelse[m[K’
  715 |         } [01;31m[Kelse[m[K if (currentPhase == PHASE_TRACK_ACCURACY) {
      |           [01;31m[K^~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:720:9:[m[K [01;31m[Kerror: [m[K‘[01m[KactionModelData[m[K’ does not name a type
  720 |         [01;31m[KactionModelData[m[K.model = 10;
      |         [01;31m[K^~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:721:9:[m[K [01;31m[Kerror: [m[K‘[01m[KactionHeadvelControl[m[K’ does not name a type
  721 |         [01;31m[KactionHeadvelControl[m[K.model = 2;
      |         [01;31m[K^~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:722:9:[m[K [01;31m[Kerror: [m[K‘[01m[KactionHeadvelControl[m[K’ does not name a type
  722 |         [01;31m[KactionHeadvelControl[m[K.heading = anglevel[0];
      |         [01;31m[K^~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:723:9:[m[K [01;31m[Kerror: [m[K‘[01m[KactionHeadvelControl[m[K’ does not name a type
  723 |         [01;31m[KactionHeadvelControl[m[K.velocity = anglevel[1];
      |         [01;31m[K^~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:724:1:[m[K [01;31m[Kerror: [m[Kexpected declaration before ‘[01m[K}[m[K’ token
  724 | [01;31m[K}[m[K
      | [01;31m[K^[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kfloat MotionPlan::EvaluatePathSafety(float*, float, float*)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:978:87:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtargetPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
  978 | float MotionPlan::EvaluatePathSafety(float currentPos[2], float candidateAngle, [01;35m[Kfloat targetPos[2][m[K)
      |                                                                                 [01;35m[K~~~~~~^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kfloat MotionPlan::GetObstacleDistance(float*, float*)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1038:45:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KcurrentPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
 1038 | float MotionPlan::GetObstacleDistance([01;35m[Kfloat currentPos[2][m[K, float targetPos[2])
      |                                       [01;35m[K~~~~~~^~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1038:66:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KtargetPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
 1038 | float MotionPlan::GetObstacleDistance(float currentPos[2], [01;35m[Kfloat targetPos[2][m[K)
      |                                                            [01;35m[K~~~~~~^~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kbool MotionPlan::CheckMissionFailure(float*)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1059:44:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[KcurrentPos[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
 1059 | bool MotionPlan::CheckMissionFailure([01;35m[Kfloat currentPos[2][m[K)
      |                                      [01;35m[K~~~~~~^~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:[m[K In member function ‘[01m[Kfloat MotionPlan::CalculateDetourAngle(float*, float*, float)[m[K’:
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1185:26:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[K(((double)(* start)) + (((double)2.0e+2f) * cos(((((double)left_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))[m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
 1185 |                 [01;35m[Kstart[0] + 200.0f * cos(left_angle * M_PI / 180.0f)[m[K,
      |                 [01;35m[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1186:26:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[K(((double)(*(start + 4))) + (((double)2.0e+2f) * sin(((((double)left_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))[m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
 1186 |                 [01;35m[Kstart[1] + 200.0f * sin(left_angle * M_PI / 180.0f)[m[K
      |                 [01;35m[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1192:26:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[K(((double)(* start)) + (((double)2.0e+2f) * cos(((((double)right_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))[m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
 1192 |                 [01;35m[Kstart[0] + 200.0f * cos(right_angle * M_PI / 180.0f)[m[K,
      |                 [01;35m[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1193:26:[m[K [01;35m[Kwarning: [m[Knarrowing conversion of ‘[01m[K(((double)(*(start + 4))) + (((double)2.0e+2f) * sin(((((double)right_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))[m[K’ from ‘[01m[Kdouble[m[K’ to ‘[01m[Kfloat[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing-Wnarrowing]8;;[m[K]
 1193 |                 [01;35m[Kstart[1] + 200.0f * sin(right_angle * M_PI / 180.0f)[m[K
      |                 [01;35m[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[01m[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1173:62:[m[K [01;35m[Kwarning: [m[Kunused parameter ‘[01m[Kend[m[K’ [[01;35m[K]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter-Wunused-parameter]8;;[m[K]
 1173 | float MotionPlan::CalculateDetourAngle(float start[2], [01;35m[Kfloat end[2][m[K, float direct_angle)
      |                                                        [01;35m[K~~~~~~^~~~~~[m[K
gmake[2]: *** [CMakeFiles/planning_hpp.dir/build.make:90：CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o] 错误 1
gmake[1]: *** [CMakeFiles/Makefile2:139：CMakeFiles/planning_hpp.dir/all] 错误 2
gmake: *** [Makefile:146：all] 错误 2
