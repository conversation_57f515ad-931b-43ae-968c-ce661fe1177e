[0.000000] (-) TimerEvent: {}
[0.000237] (xt_user) JobQueued: {'identifier': 'xt_user', 'dependencies': OrderedDict()}
[0.000304] (xt_user) JobStarted: {'identifier': 'xt_user'}
[0.006065] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'cmake'}
[0.006954] (xt_user) JobProgress: {'identifier': 'xt_user', 'progress': 'build'}
[0.008158] (xt_user) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('LANGUAGE', 'zh_CN:zh'), ('USER', 'xumj'), ('XDG_SESSION_TYPE', 'wayland'), ('GIT_ASKPASS', '/usr/share/code/resources/app/extensions/git/dist/askpass.sh'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/x86_64-linux-gnu:/opt/ros/humble/lib'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('CHROME_DESKTOP', 'code.desktop'), ('OLDPWD', '/home/<USER>/project2_new（复件）/USVControl-user'), ('TERM_PROGRAM_VERSION', '1.102.3'), ('DESKTOP_SESSION', 'ubuntu'), ('GIO_LAUNCHED_DESKTOP_FILE', '/usr/share/applications/code.desktop'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/usr/share/code/resources/app/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/usr/share/code/code'), ('MANAGERPID', '1612'), ('DBUS_STARTER_BUS_TYPE', 'session'), ('SYSTEMD_EXEC_PID', '1753'), ('IM_CONFIG_CHECK_ENV', '1'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus,guid=5ce7623966f63025ba1a23456894c89d'), ('COLORTERM', 'truecolor'), ('GIO_LAUNCHED_DESKTOP_FILE_PID', '237450'), ('IM_CONFIG_PHASE', '1'), ('WAYLAND_DISPLAY', 'wayland-0'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'xumj'), ('JOURNAL_STREAM', '8:24796'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'xumj'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/snap/bin'), ('SESSION_MANAGER', 'local/xumj-virtual-machine:@/tmp/.ICE-unix/1753,unix/xumj-virtual-machine:/tmp/.ICE-unix/1753'), ('INVOCATION_ID', 'dfbaa205b2d649429eddca64e158508e'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_SETUP_DISPLAY', ':1'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('GDK_BACKEND', 'x11'), ('DISPLAY', ':0'), ('LANG', 'zh_CN.UTF-8'), ('XDG_CURRENT_DESKTOP', 'Unity'), ('XMODIFIERS', '@im=ibus'), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/.mutter-Xwaylandauth.FTM3A3'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/1000/vscode-git-2577d93802.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AGENT_LAUNCHER', 'gnome-keyring'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/humble'), ('ORIGINAL_XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/project2_new（复件）/USVControl-user/build/xt_user'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('DBUS_STARTER_ADDRESS', 'unix:path=/run/user/1000/bus,guid=5ce7623966f63025ba1a23456894c89d'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/humble')]), 'shell': False}
[0.020415] (xt_user) StderrLine: {'line': b'\x1b[0mCMake Error: The current CMakeCache.txt directory /home/<USER>/project2_new\xef\xbc\x88\xe5\xa4\x8d\xe4\xbb\xb6\xef\xbc\x89/USVControl-user/build/xt_user/CMakeCache.txt is different than the directory /home/<USER>/project2_new/USVControl-user/build/xt_user where CMakeCache.txt was created. This may result in binaries being created in the wrong place. If you are not sure, reedit the CMakeCache.txt\x1b[0m\n'}
[0.038929] (xt_user) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.098186] (-) TimerEvent: {}
[0.160096] (xt_user) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.185919] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.187841] (xt_user) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.193380] (xt_user) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.199108] (-) TimerEvent: {}
[0.202846] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.216383] (xt_user) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.243903] (xt_user) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.245116] (xt_user) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.300765] (-) TimerEvent: {}
[0.332698] (xt_user) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.376593] (xt_user) StdoutLine: {'line': b'-- Found cv_bridge: 3.2.1 (/opt/ros/humble/share/cv_bridge/cmake)\n'}
[0.401215] (-) TimerEvent: {}
[0.470861] (xt_user) StdoutLine: {'line': b'-- Configuring done\n'}
[0.484982] (xt_user) StdoutLine: {'line': b'-- Generating done\n'}
[0.490781] (xt_user) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/project2_new/USVControl-user/build/xt_user\n'}
[0.501478] (-) TimerEvent: {}
[0.521725] (xt_user) StdoutLine: {'line': b'\x1b[35m\x1b[1mConsolidate compiler generated dependencies of target planning_hpp\x1b[0m\n'}
[0.535288] (xt_user) StdoutLine: {'line': b'[ 14%] \x1b[32mBuilding CXX object CMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o\x1b[0m\n'}
[0.602003] (-) TimerEvent: {}
[0.703331] (-) TimerEvent: {}
[0.804968] (-) TimerEvent: {}
[0.906094] (-) TimerEvent: {}
[0.967745] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In constructor \xe2\x80\x98\x1b[01m\x1b[KMotionPlan::MotionPlan()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.968003] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:36:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kvoid* memset(void*, int, size_t)\x1b[m\x1b[K\xe2\x80\x99 clearing an object of type \xe2\x80\x98\x1b[01m\x1b[Kstruct ObstacleAvoidanceParam\x1b[m\x1b[K\xe2\x80\x99 with no trivial copy-assignment; use assignment or value-initialization instead [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wclass-memaccess\x07-Wclass-memaccess\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.968077] (xt_user) StderrLine: {'line': b'   36 |         \x1b[01;35m\x1b[Kmemset(&obstacleAvoidance, 0, sizeof(ObstacleAvoidanceParam))\x1b[m\x1b[K;\n'}
[0.968147] (xt_user) StderrLine: {'line': b'      |         \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.968207] (xt_user) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1\x1b[m\x1b[K:\n'}
[0.968267] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.h:50:1:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kstruct ObstacleAvoidanceParam\x1b[m\x1b[K\xe2\x80\x99 declared here\n'}
[0.968389] (xt_user) StderrLine: {'line': b'   50 | \x1b[01;36m\x1b[K{\x1b[m\x1b[K\n'}
[0.968450] (xt_user) StderrLine: {'line': b'      | \x1b[01;36m\x1b[K^\x1b[m\x1b[K\n'}
[0.971534] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajecotryGet(TransState, anchor_mission_control)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.971762] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:261:43:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kboatstate\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.971828] (xt_user) StderrLine: {'line': b'  261 | void MotionPlan::TrajecotryGet(\x1b[01;35m\x1b[KTransState boatstate\x1b[m\x1b[K, anchor_mission_control task)\n'}
[0.971881] (xt_user) StderrLine: {'line': b'      |                                \x1b[01;35m\x1b[K~~~~~~~~~~~^~~~~~~~~\x1b[m\x1b[K\n'}
[0.971932] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid MotionPlan::TrajectoryTrackingDirect(TransState)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[0.972002] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:344:47:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[1]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.972077] (xt_user) StderrLine: {'line': b'  344 |         float pos[2] = { \x1b[01;35m\x1b[Kboatstate.PostoHome[1]\x1b[m\x1b[K, boatstate.PostoHome[0] }; // [X, Y] = [lon, lat]\n'}
[0.972361] (xt_user) StderrLine: {'line': b'      |                          \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[0.972583] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:344:71:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[Kboatstate.TransState::PostoHome[0]\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[0.972890] (xt_user) StderrLine: {'line': b'  344 |         float pos[2] = { boatstate.PostoHome[1], \x1b[01;35m\x1b[Kboatstate.PostoHome[0]\x1b[m\x1b[K }; // [X, Y] = [lon, lat]\n'}
[0.972955] (xt_user) StderrLine: {'line': b'      |                                                  \x1b[01;35m\x1b[K~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[0.973903] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:451:15:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kredeclaration of \xe2\x80\x98\x1b[01m\x1b[Kfloat p_end [2]\x1b[m\x1b[K\xe2\x80\x99\n'}
[0.974125] (xt_user) StderrLine: {'line': b'  451 |         float \x1b[01;31m\x1b[Kp_end\x1b[m\x1b[K[2] = { tracking_task.mission[dotTrackingParam.TrackNum].lon,\n'}
[0.974182] (xt_user) StderrLine: {'line': b'      |               \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[0.974230] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:376:15:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kfloat p_end [2]\x1b[m\x1b[K\xe2\x80\x99 previously declared here\n'}
[0.974303] (xt_user) StderrLine: {'line': b'  376 |         float \x1b[01;36m\x1b[Kp_end\x1b[m\x1b[K[2] = {\n'}
[0.974356] (xt_user) StderrLine: {'line': b'      |               \x1b[01;36m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[0.974405] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:453:15:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kredeclaration of \xe2\x80\x98\x1b[01m\x1b[Kfloat distance_to_target\x1b[m\x1b[K\xe2\x80\x99\n'}
[0.974456] (xt_user) StderrLine: {'line': b'  453 |         float \x1b[01;31m\x1b[Kdistance_to_target\x1b[m\x1b[K = sqrt((p_end[0] - pos[0])*(p_end[0] - pos[0]) +\n'}
[0.974505] (xt_user) StderrLine: {'line': b'      |               \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.974555] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:380:15:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kfloat distance_to_target\x1b[m\x1b[K\xe2\x80\x99 previously declared here\n'}
[0.974606] (xt_user) StderrLine: {'line': b'  380 |         float \x1b[01;36m\x1b[Kdistance_to_target\x1b[m\x1b[K = sqrtf(powf(p_end[0] - pos[0], 2) + powf(p_end[1] - pos[1], 2));\n'}
[0.974658] (xt_user) StderrLine: {'line': b'      |               \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.974708] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:460:15:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kredeclaration of \xe2\x80\x98\x1b[01m\x1b[Kfloat current_switch_distance\x1b[m\x1b[K\xe2\x80\x99\n'}
[0.974814] (xt_user) StderrLine: {'line': b'  460 |         float \x1b[01;31m\x1b[Kcurrent_switch_distance\x1b[m\x1b[K = 50.0f; // \xe9\xbb\x98\xe8\xae\xa450\xe7\xb1\xb3\xe5\x88\x87\xe6\x8d\xa2\xe8\xb7\x9d\xe7\xa6\xbb\n'}
[0.974873] (xt_user) StderrLine: {'line': b'      |               \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.974928] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:383:15:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kfloat current_switch_distance\x1b[m\x1b[K\xe2\x80\x99 previously declared here\n'}
[0.974981] (xt_user) StderrLine: {'line': b'  383 |         float \x1b[01;36m\x1b[Kcurrent_switch_distance\x1b[m\x1b[K = (currentPhase == PHASE_TRACK_ACCURACY) ? 5.0f : dotTrackingParam.switchlen;\n'}
[0.975044] (xt_user) StderrLine: {'line': b'      |               \x1b[01;36m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.977913] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:471:54:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kt\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope; did you mean \xe2\x80\x98\x1b[01m\x1b[Ktm\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.978164] (xt_user) StderrLine: {'line': b'  471 |                             float check_y = pos[1] + \x1b[01;31m\x1b[Kt\x1b[m\x1b[K * dy;\n'}
[0.978226] (xt_user) StderrLine: {'line': b'      |                                                      \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[0.978278] (xt_user) StderrLine: {'line': b'      |                                                      \x1b[32m\x1b[Ktm\x1b[m\x1b[K\n'}
[0.980740] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:471:58:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kdy\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[0.980975] (xt_user) StderrLine: {'line': b'  471 |                             float check_y = pos[1] + t * \x1b[01;31m\x1b[Kdy\x1b[m\x1b[K;\n'}
[0.981041] (xt_user) StderrLine: {'line': b'      |                                                          \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[0.985873] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:476:62:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kcheck_x\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope; did you mean \xe2\x80\x98\x1b[01m\x1b[Kcheck_y\x1b[m\x1b[K\xe2\x80\x99?\n'}
[0.986805] (xt_user) StderrLine: {'line': b'  476 |                                     if (IsObstacleAtPosition(\x1b[01;31m\x1b[Kcheck_x\x1b[m\x1b[K + ox, check_y + oy)) {\n'}
[0.986950] (xt_user) StderrLine: {'line': b'      |                                                              \x1b[01;31m\x1b[K^~~~~~~\x1b[m\x1b[K\n'}
[0.987014] (xt_user) StderrLine: {'line': b'      |                                                              \x1b[32m\x1b[Kcheck_y\x1b[m\x1b[K\n'}
[0.992346] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:477:41:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kdirect_path_safe\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[0.992539] (xt_user) StderrLine: {'line': b'  477 |                                         \x1b[01;31m\x1b[Kdirect_path_safe\x1b[m\x1b[K = false;\n'}
[0.992583] (xt_user) StderrLine: {'line': b'      |                                         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[0.997034] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:482:38:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kdirect_path_safe\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[0.997323] (xt_user) StderrLine: {'line': b'  482 |                                 if (!\x1b[01;31m\x1b[Kdirect_path_safe\x1b[m\x1b[K) break;\n'}
[0.997379] (xt_user) StderrLine: {'line': b'      |                                      \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.004523] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:484:34:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[Kdirect_path_safe\x1b[m\x1b[K\xe2\x80\x99 was not declared in this scope\n'}
[1.005215] (xt_user) StderrLine: {'line': b'  484 |                             if (!\x1b[01;31m\x1b[Kdirect_path_safe\x1b[m\x1b[K) break;\n'}
[1.005309] (xt_user) StderrLine: {'line': b'      |                                  \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.005375] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:484:52:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kbreak statement not within loop or switch\n'}
[1.005436] (xt_user) StderrLine: {'line': b'  484 |                             if (!direct_path_safe) \x1b[01;31m\x1b[Kbreak\x1b[m\x1b[K;\n'}
[1.005496] (xt_user) StderrLine: {'line': b'      |                                                    \x1b[01;31m\x1b[K^~~~~\x1b[m\x1b[K\n'}
[1.005553] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:380:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kdistance_to_target\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.005617] (xt_user) StderrLine: {'line': b'  380 |         float \x1b[01;35m\x1b[Kdistance_to_target\x1b[m\x1b[K = sqrtf(powf(p_end[0] - pos[0], 2) + powf(p_end[1] - pos[1], 2));\n'}
[1.005677] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.005735] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:383:15:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused variable \xe2\x80\x98\x1b[01m\x1b[Kcurrent_switch_distance\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-variable\x07-Wunused-variable\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.005796] (xt_user) StderrLine: {'line': b'  383 |         float \x1b[01;35m\x1b[Kcurrent_switch_distance\x1b[m\x1b[K = (currentPhase == PHASE_TRACK_ACCURACY) ? 5.0f : dotTrackingParam.switchlen;\n'}
[1.005989] (xt_user) StderrLine: {'line': b'      |               \x1b[01;35m\x1b[K^~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.006172] (-) TimerEvent: {}
[1.006300] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K At global scope:\n'}
[1.006375] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:487:17:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[1.006437] (xt_user) StderrLine: {'line': b'  487 |                 \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[1.006497] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[1.006561] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:490:17:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kif\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.006621] (xt_user) StderrLine: {'line': b'  490 |                 \x1b[01;31m\x1b[Kif\x1b[m\x1b[K (direct_path_safe && !emergency_cheat_mode && dotTrackingParam.TrackNum < 2) {\n'}
[1.006679] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[1.006738] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:495:19:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kelse\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.006791] (xt_user) StderrLine: {'line': b'  495 |                 } \x1b[01;31m\x1b[Kelse\x1b[m\x1b[K {\n'}
[1.006843] (xt_user) StderrLine: {'line': b'      |                   \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[1.006902] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:624:13:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[1.006969] (xt_user) StderrLine: {'line': b'  624 |             \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[1.007029] (xt_user) StderrLine: {'line': b'      |             \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[1.007087] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:625:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[1.007153] (xt_user) StderrLine: {'line': b'  625 |         \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[1.007284] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[1.007447] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:628:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kif\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.007623] (xt_user) StderrLine: {'line': b'  628 |         \x1b[01;31m\x1b[Kif\x1b[m\x1b[K (distance_to_target <= current_switch_distance) {\n'}
[1.007681] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[1.007804] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:654:11:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kelse\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.007903] (xt_user) StderrLine: {'line': b'  654 |         } \x1b[01;31m\x1b[Kelse\x1b[m\x1b[K {\n'}
[1.007967] (xt_user) StderrLine: {'line': b'      |           \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[1.008029] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:664:5:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[1.008099] (xt_user) StderrLine: {'line': b'  664 |     \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[1.008162] (xt_user) StderrLine: {'line': b'      |     \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[1.008337] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:667:5:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kif\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.008447] (xt_user) StderrLine: {'line': b'  667 |     \x1b[01;31m\x1b[Kif\x1b[m\x1b[K (dotTrackingParam.TrackNum > phaseTransitionFlag) {\n'}
[1.008560] (xt_user) StderrLine: {'line': b'      |     \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[1.008674] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:713:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kif\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.008751] (xt_user) StderrLine: {'line': b'  713 |         \x1b[01;31m\x1b[Kif\x1b[m\x1b[K (currentPhase == PHASE_HEADING_CONTROL) {\n'}
[1.008809] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~\x1b[m\x1b[K\n'}
[1.008866] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:715:11:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected unqualified-id before \xe2\x80\x98\x1b[01m\x1b[Kelse\x1b[m\x1b[K\xe2\x80\x99\n'}
[1.008947] (xt_user) StderrLine: {'line': b'  715 |         } \x1b[01;31m\x1b[Kelse\x1b[m\x1b[K if (currentPhase == PHASE_TRACK_ACCURACY) {\n'}
[1.008983] (xt_user) StderrLine: {'line': b'      |           \x1b[01;31m\x1b[K^~~~\x1b[m\x1b[K\n'}
[1.009051] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:720:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KactionModelData\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[1.009125] (xt_user) StderrLine: {'line': b'  720 |         \x1b[01;31m\x1b[KactionModelData\x1b[m\x1b[K.model = 10;\n'}
[1.009252] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.009428] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:721:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KactionHeadvelControl\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[1.009472] (xt_user) StderrLine: {'line': b'  721 |         \x1b[01;31m\x1b[KactionHeadvelControl\x1b[m\x1b[K.model = 2;\n'}
[1.009512] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.009549] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:722:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KactionHeadvelControl\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[1.009585] (xt_user) StderrLine: {'line': b'  722 |         \x1b[01;31m\x1b[KactionHeadvelControl\x1b[m\x1b[K.heading = anglevel[0];\n'}
[1.009626] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.009808] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:723:9:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[K\xe2\x80\x98\x1b[01m\x1b[KactionHeadvelControl\x1b[m\x1b[K\xe2\x80\x99 does not name a type\n'}
[1.009919] (xt_user) StderrLine: {'line': b'  723 |         \x1b[01;31m\x1b[KactionHeadvelControl\x1b[m\x1b[K.velocity = anglevel[1];\n'}
[1.009985] (xt_user) StderrLine: {'line': b'      |         \x1b[01;31m\x1b[K^~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.010046] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:724:1:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kexpected declaration before \xe2\x80\x98\x1b[01m\x1b[K}\x1b[m\x1b[K\xe2\x80\x99 token\n'}
[1.010104] (xt_user) StderrLine: {'line': b'  724 | \x1b[01;31m\x1b[K}\x1b[m\x1b[K\n'}
[1.010164] (xt_user) StderrLine: {'line': b'      | \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[1.033070] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::EvaluatePathSafety(float*, float, float*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.033870] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:978:87:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KtargetPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.034315] (xt_user) StderrLine: {'line': b'  978 | float MotionPlan::EvaluatePathSafety(float currentPos[2], float candidateAngle, \x1b[01;35m\x1b[Kfloat targetPos[2]\x1b[m\x1b[K)\n'}
[1.034752] (xt_user) StderrLine: {'line': b'      |                                                                                 \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.035215] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::GetObstacleDistance(float*, float*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.035287] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1038:45:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.035352] (xt_user) StderrLine: {'line': b' 1038 | float MotionPlan::GetObstacleDistance(\x1b[01;35m\x1b[Kfloat currentPos[2]\x1b[m\x1b[K, float targetPos[2])\n'}
[1.035409] (xt_user) StderrLine: {'line': b'      |                                       \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.035464] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1038:66:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KtargetPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.035655] (xt_user) StderrLine: {'line': b' 1038 | float MotionPlan::GetObstacleDistance(float currentPos[2], \x1b[01;35m\x1b[Kfloat targetPos[2]\x1b[m\x1b[K)\n'}
[1.035758] (xt_user) StderrLine: {'line': b'      |                                                            \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.035824] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kbool MotionPlan::CheckMissionFailure(float*)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.035884] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1059:44:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[KcurrentPos\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.035946] (xt_user) StderrLine: {'line': b' 1059 | bool MotionPlan::CheckMissionFailure(\x1b[01;35m\x1b[Kfloat currentPos[2]\x1b[m\x1b[K)\n'}
[1.036012] (xt_user) StderrLine: {'line': b'      |                                      \x1b[01;35m\x1b[K~~~~~~^~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.037560] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kfloat MotionPlan::CalculateDetourAngle(float*, float*, float)\x1b[m\x1b[K\xe2\x80\x99:\n'}
[1.038741] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1185:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[K(((double)(* start)) + (((double)2.0e+2f) * cos(((((double)left_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.039011] (xt_user) StderrLine: {'line': b' 1185 |                 \x1b[01;35m\x1b[Kstart[0] + 200.0f * cos(left_angle * M_PI / 180.0f)\x1b[m\x1b[K,\n'}
[1.039112] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.039167] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1186:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[K(((double)(*(start + 4))) + (((double)2.0e+2f) * sin(((((double)left_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.039238] (xt_user) StderrLine: {'line': b' 1186 |                 \x1b[01;35m\x1b[Kstart[1] + 200.0f * sin(left_angle * M_PI / 180.0f)\x1b[m\x1b[K\n'}
[1.039289] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.039339] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1192:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[K(((double)(* start)) + (((double)2.0e+2f) * cos(((((double)right_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.039396] (xt_user) StderrLine: {'line': b' 1192 |                 \x1b[01;35m\x1b[Kstart[0] + 200.0f * cos(right_angle * M_PI / 180.0f)\x1b[m\x1b[K,\n'}
[1.039452] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.039501] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1193:26:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Knarrowing conversion of \xe2\x80\x98\x1b[01m\x1b[K(((double)(*(start + 4))) + (((double)2.0e+2f) * sin(((((double)right_angle) * 3.1415926535897931e+0) / ((double)1.8e+2f)))))\x1b[m\x1b[K\xe2\x80\x99 from \xe2\x80\x98\x1b[01m\x1b[Kdouble\x1b[m\x1b[K\xe2\x80\x99 to \xe2\x80\x98\x1b[01m\x1b[Kfloat\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wnarrowing\x07-Wnarrowing\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.039583] (xt_user) StderrLine: {'line': b' 1193 |                 \x1b[01;35m\x1b[Kstart[1] + 200.0f * sin(right_angle * M_PI / 180.0f)\x1b[m\x1b[K\n'}
[1.039634] (xt_user) StderrLine: {'line': b'      |                 \x1b[01;35m\x1b[K~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\x1b[m\x1b[K\n'}
[1.039683] (xt_user) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/project2_new/USVControl-user/ControlNode/planning/motion_plan/motion_plan.cpp:1173:62:\x1b[m\x1b[K \x1b[01;35m\x1b[Kwarning: \x1b[m\x1b[Kunused parameter \xe2\x80\x98\x1b[01m\x1b[Kend\x1b[m\x1b[K\xe2\x80\x99 [\x1b[01;35m\x1b[K\x1b]8;;https://gcc.gnu.org/onlinedocs/gcc/Warning-Options.html#index-Wunused-parameter\x07-Wunused-parameter\x1b]8;;\x07\x1b[m\x1b[K]\n'}
[1.039739] (xt_user) StderrLine: {'line': b' 1173 | float MotionPlan::CalculateDetourAngle(float start[2], \x1b[01;35m\x1b[Kfloat end[2]\x1b[m\x1b[K, float direct_angle)\n'}
[1.039794] (xt_user) StderrLine: {'line': b'      |                                                        \x1b[01;35m\x1b[K~~~~~~^~~~~~\x1b[m\x1b[K\n'}
[1.106535] (-) TimerEvent: {}
[1.133881] (xt_user) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/planning_hpp.dir/build.make:90\xef\xbc\x9aCMakeFiles/planning_hpp.dir/motion_plan/motion_plan.cpp.o] \xe9\x94\x99\xe8\xaf\xaf 1\n'}
[1.134255] (xt_user) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:139\xef\xbc\x9aCMakeFiles/planning_hpp.dir/all] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[1.134324] (xt_user) StderrLine: {'line': b'gmake: *** [Makefile:146\xef\xbc\x9aall] \xe9\x94\x99\xe8\xaf\xaf 2\n'}
[1.138446] (xt_user) CommandEnded: {'returncode': 2}
[1.151750] (xt_user) JobEnded: {'identifier': 'xt_user', 'rc': 2}
[1.163048] (-) EventReactorShutdown: {}
