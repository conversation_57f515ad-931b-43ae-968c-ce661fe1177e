#include "motion_plan.h"
#include <cmath>
#include <cstdio>
#include <iostream>
#include <vector>     // 确保vector被包含
#include <utility>    // 确保pair被包含

/**
 * @file motion_plan.cpp
 * @brief 运动规划实现文件
 */

MotionPlan::MotionPlan()
{
	// 初始化轨迹跟踪参数
	memset(&dotTrackingParam, 0, sizeof(TrackingPlanParam));
	dotTrackingParam.switchlen = 50.0f; // 默认切换距离
	dotTrackingParam.maxSpeed = 20.0f;
	dotTrackingParam.eta = 0.1;
	dotTrackingParam.deltat = 0.1;
	
	// 初始化其他状态
	memset(&tracking_task, 0, sizeof(anchor_mission_control));
	memset(&actionModelData, 0, sizeof(flash_model_data));
	memset(&actionActuatorControl, 0, sizeof(actuator_control));
	memset(&actionHeadvelControl, 0, sizeof(actuator_control));
	
	// 初始化评估和阶段管理
	currentPhase = PHASE_HEADING_CONTROL;
	phaseTransitionFlag = 0;
	trackAccuracyEval.evalStarted = false;
	headingEvalStarted = false;

	// 初始化避障参数
	obstacleAvoidance.obstacleDetected = false;
	obstacleAvoidance.shipDetected = false;
	obstacleAvoidance.minObstacleDistance = 1000.0f;
	obstacleAvoidance.minShipDistance = 1000.0f;
	obstacleAvoidance.avoidanceAngle = 0.0f;
	obstacleAvoidance.missionFailed = false;
	obstacleAvoidance.nearbyShips.clear();
	localMapPtr = nullptr;
}

/**
 * @brief 航向控制精度评估函数
 * @param currentHeading 当前航向角
 * @param targetHeading 目标航向角
 * @description 根据比赛要求进行航向控制精度评估
 *              任务运行10s后，每隔1s采集一次航向数据，连续采集10次
 *              然后计算这10次航向与目标航向的均方根误差
 */
void MotionPlan::EvaluateHeadingAccuracy(float currentHeading, float targetHeading)
{
	// 更新任务运行时间（假设每次调用这个函数间隔为0.1秒，即控制周期）
	taskRunningTime += 1;
	
	// 每10个控制周期等于1秒
	int currentTimeInSeconds = taskRunningTime / 10;
	
	// 任务运行10秒后开始评估
	if (currentTimeInSeconds >= 10 && !headingEvalStarted) {
		headingEvalStarted = true;
		this->targetHeading = targetHeading; // 保存目标航向
		printf("开始航向控制精度评估，目标航向: %.2f°\n", targetHeading);
	}
	
	// 开始评估过程
	if (headingEvalStarted) {
		// 每隔1秒采集一次航向数据
		if (currentTimeInSeconds > lastSampleTime) {
			lastSampleTime = currentTimeInSeconds;
			
			if (headingDataCount < 10) {
				// 记录当前航向数据
				headingHistory[headingDataCount] = currentHeading;
				headingDataCount++;
				printf("采集航向数据 #%d: %.2f° (目标航向: %.2f°)\n", 
					headingDataCount, currentHeading, targetHeading);
				
				// 开始航向评估过程
				headingEvalInProcess = true;
			}
			
			// 采集满10个数据后计算精度
			if (headingDataCount == 10 && headingEvalInProcess) {
				CalculateHeadingAccuracy();
				// 重置采集，准备下一轮评估（每10秒一次）
				headingDataCount = 0;
				headingEvalInProcess = false;
			}
		}
	}
}

/**
 * @brief 计算并输出航向控制精度
 * @description 根据采集的10个航向数据计算控制精度
 *              使用公式: h_e = sqrt(∑(h_i - h_0)^2 / 10)
 */
void MotionPlan::CalculateHeadingAccuracy()
{
	float squareSum = 0.0f;
	
	// 计算航向偏差的平方和
	for (int i = 0; i < 10; i++) {
		// 需要处理航向角跨越360度边界的情况
		float diff = headingHistory[i] - targetHeading;
		if (diff > 180.0f) diff -= 360.0f;
		if (diff < -180.0f) diff += 360.0f;
		
		squareSum += diff * diff;
	}
	
	// 计算均方根误差
	float headingAccuracy = sqrt(squareSum / 10.0f);
	
	// 输出结果
	printf("\n=====================================================\n");
	printf("航向控制精度评估结果（任务时间：%d 秒）\n", taskRunningTime / 10);
	printf("目标航向: %.2f°\n", targetHeading);
	printf("10次航向数据: ");
	for (int i = 0; i < 10; i++) {
		printf("%.2f° ", headingHistory[i]);
	}
	printf("\n航向控制精度: %.2f°\n", headingAccuracy);
	
	// 根据比赛要求判定分数
	if (headingAccuracy <= 2.0f) {
		printf("航向控制精度合格（≤2°）\n");
	} else {
		printf("航向控制精度不合格（>2°），得分为0\n");
	}
	printf("=====================================================\n\n");
}

// void MotionPlan::EvaluateHeadingAccuracy(float currentHeading, float targetHeading)
// {
// 	taskRunningTime += 1; // 假设每100ms调用一次
// 	int currentTimeInSeconds = taskRunningTime / 10;
	
// 	if (currentTimeInSeconds >= 10 && !headingEvalStarted) {
// 		headingEvalStarted = true;
// 		this->targetHeading = targetHeading;
// 		printf("开始航向控制精度评估，目标航向: %.2f°\n", targetHeading);
// 	}
	
// 	if (headingEvalStarted && (currentTimeInSeconds > lastSampleTime)) {
// 		lastSampleTime = currentTimeInSeconds;
// 		if (headingDataCount < 10) {
// 			headingHistory[headingDataCount++] = currentHeading;
// 			printf("采集航向数据 #%d: %.2f°\n", headingDataCount, currentHeading);
// 		}
// 		if (headingDataCount == 10) {
// 			CalculateHeadingAccuracy();
// 			headingEvalStarted = false; // 只评估一次
// 		}
// 	}
// }

// void MotionPlan::CalculateHeadingAccuracy()
// {
// 	float squareSum = 0.0f;
// 	for (int i = 0; i < 10; i++) {
// 		float diff = headingHistory[i] - targetHeading;
// 		if (diff > 180.0f) diff -= 360.0f;
// 		if (diff < -180.0f) diff += 360.0f;
// 		squareSum += diff * diff;
// 	}
// 	float headingAccuracy = sqrtf(squareSum / 10.0f);
// 	printf("\n--- 航向控制精度评估结果 ---\n");
// 	printf("航向控制精度: %.2f°\n", headingAccuracy);
// 	if (headingAccuracy <= 2.0f) printf("判定: 合格\n");
// 	else printf("判定: 不合格\n");
// 	printf("-------------------------------\n\n");
// }

void MotionPlan::InitTrackAccuracyEvaluation(float startPoint[2], float endPoint[2])
{
	trackAccuracyEval.segmentStart[0] = startPoint[0];
	trackAccuracyEval.segmentStart[1] = startPoint[1];
	trackAccuracyEval.segmentEnd[0] = endPoint[0];
	trackAccuracyEval.segmentEnd[1] = endPoint[1];
	
	float dx = endPoint[0] - startPoint[0];
	float dy = endPoint[1] - startPoint[1];
	trackAccuracyEval.segmentLength = sqrtf(dx*dx + dy*dy);
	
	float midPoint[2] = {
		(startPoint[0] + endPoint[0]) / 2.0f,
		(startPoint[1] + endPoint[1]) / 2.0f
	};
	for (int i = 0; i < 10; i++) {
		float ratio = (float)i / 9.0f;
		trackAccuracyEval.idealPoints[i][0] = midPoint[0] + ratio * (endPoint[0] - midPoint[0]);
		trackAccuracyEval.idealPoints[i][1] = midPoint[1] + ratio * (endPoint[1] - midPoint[1]);
	}
	
	trackAccuracyEval.actualTrackHistory.clear();
	trackAccuracyEval.actualTrackHistory.reserve(500);
	trackAccuracyEval.evalStarted = true;
	printf("--- 航迹跟踪精度评估已初始化 ---\n");
}

void MotionPlan::EvaluateTrackAccuracy(float currentPos[2])
{
	if (trackAccuracyEval.evalStarted) {
		trackAccuracyEval.actualTrackHistory.push_back(std::make_pair(currentPos[0], currentPos[1]));
	}
}

void MotionPlan::CalculateTrackAccuracy()
{
	if (trackAccuracyEval.actualTrackHistory.empty()) {
		printf("警告：实际航迹历史为空，无法计算精度。\n");
		return;
	}

	float squareSum = 0.0f;
    int valid_pairs = 0;

	for (int i = 0; i < 10; i++) {
		float min_dist_sq = -1.0f;
		
		for (size_t j = 0; j < trackAccuracyEval.actualTrackHistory.size(); j++) {
			float dx = trackAccuracyEval.actualTrackHistory[j].first - trackAccuracyEval.idealPoints[i][0];
			float dy = trackAccuracyEval.actualTrackHistory[j].second - trackAccuracyEval.idealPoints[i][1];
			float dist_sq = dx*dx + dy*dy;
			if (min_dist_sq < 0 || dist_sq < min_dist_sq) {
				min_dist_sq = dist_sq;
			}
		}

        if (min_dist_sq >= 0) {
            squareSum += min_dist_sq;
            valid_pairs++;
        }
	}
	
	if (valid_pairs == 0) {
		printf("警告：未能成功配对任何数据点。\n");
		return;
	}
	
	float trackAccuracy = sqrtf(squareSum / valid_pairs);
	
	printf("\n--- 航迹跟踪精度评估结果 ---\n");
    printf("共记录了 %zu 个实际航迹点，配对 %d/10 个理想点\n", trackAccuracyEval.actualTrackHistory.size(), valid_pairs);
	printf("航迹跟踪精度 (RMSE): %.4f 米\n", trackAccuracy);
	
	float vesselLength = 80.0f;
	if (trackAccuracy <= 0.5f * vesselLength) {
		printf("判定: 合格 (≤%.1f米)\n", 0.5f * vesselLength);
	} else {
		printf("判定: 不合格 (>%.1f米)\n", 0.5f * vesselLength);
	}
	printf("-------------------------------\n\n");
}

void MotionPlan::TrajecotryGet(TransState boatstate, anchor_mission_control task)
{
	memset(&dotTrackingParam, 0, sizeof(TrackingPlanParam));
	unsigned int seq_num = 0;
	for (int i = 0; i < task.mission_num; i++) {
		if (task.mission[i].command == 16) {
			tracking_task.mission[seq_num] = task.mission[i];
			tracking_task.mission[seq_num].seq = seq_num;
			seq_num++;
		}
	}
	tracking_task.mission_num = seq_num;

	dotTrackingParam.DotNum = tracking_task.mission_num;
	dotTrackingParam.TrackNum = 0;
	dotTrackingParam.switchlen = 50.0f; // 默认切换距离
	
	// 重置所有状态和评估
	currentPhase = PHASE_HEADING_CONTROL;
	phaseTransitionFlag = 0;
	trackAccuracyEval.evalStarted = false;
	trackAccuracyEval.actualTrackHistory.clear();
	headingEvalStarted = false;
	headingDataCount = 0;
	taskRunningTime = 0;
	lastSampleTime = 0;

	printf("任务已加载，共 %d 个航点。当前阶段: 航向控制 (->航点0)\n", dotTrackingParam.DotNum);
}

void MotionPlan::TrajectoryTrackingDirect(TransState boatstate)
{
	// 1. 获取当前位置
	float pos[2] = { (float)boatstate.PostoHome[1], (float)boatstate.PostoHome[0] }; // [X, Y] = [lon, lat]
	float anglevel[2] = {0, 0};
	float tarangle = 0;

	// 2. 检查任务是否已完成
	if (currentPhase == PHASE_MISSION_COMPLETE) {
		// 保持停止状态
		actionModelData.model = 10;
		actionHeadvelControl.model = 2;
		actionHeadvelControl.heading = 0;
		actionHeadvelControl.velocity = 0;
		return;
	}

	// 3. 定义当前目标点和计算距离
	float p_end[2] = {
		tracking_task.mission[dotTrackingParam.TrackNum].lon,
		tracking_task.mission[dotTrackingParam.TrackNum].lat
	};
	float distance_to_target = sqrtf(powf(p_end[0] - pos[0], 2) + powf(p_end[1] - pos[1], 2));

	// 4. 根据当前阶段，决定切换距离
	float current_switch_distance = (currentPhase == PHASE_TRACK_ACCURACY) ? 5.0f : dotTrackingParam.switchlen;

	// 5. 核心算法选择
	if (currentPhase == PHASE_HEADING_CONTROL || currentPhase == PHASE_CURVE_TRACKING) {
		// 使用LOS算法：直接朝向目标点
		float errpos[2] = { p_end[0] - pos[0], p_end[1] - pos[1] };
		tarangle = atan2f(errpos[0], errpos[1]) * 180.0f / M_PI;
	}
	else if (currentPhase == PHASE_TRACK_ACCURACY) {
		// 使用ILOS算法：精确跟踪直线
		float p_start[2] = {
			tracking_task.mission[dotTrackingParam.TrackNum - 1].lon,
			tracking_task.mission[dotTrackingParam.TrackNum - 1].lat
		};
		float path_angle_rad = atan2f(p_end[0] - p_start[0], p_end[1] - p_start[1]);

		float dx1 = p_end[0] - p_start[0], dy1 = p_end[1] - p_start[1];
		float dx2 = pos[0] - p_start[0], dy2 = pos[1] - p_start[1];
		float cross_product_z = dx2 * dy1 - dy2 * dx1;
		float segment_length = sqrtf(dx1*dx1 + dy1*dy1);
		float cte = (segment_length > 1e-6) ? (cross_product_z / segment_length) : 0;

		float delta = 5.0f * 80.0f; // 5倍船长前视距离
		float look_ahead_angle_rad = atanf(-cte / delta);
		float target_heading_rad = path_angle_rad + look_ahead_angle_rad;
		tarangle = target_heading_rad * 180.0f / M_PI;
	}

	// 角度归一化
	if (tarangle < 0) { tarangle += 360; }

	// 6. 避障处理 (从第二个航点开始)
	if (dotTrackingParam.TrackNum >= 1) {
		// 检查任务失败条件
		if (CheckMissionFailure(pos)) {
			printf("任务失败，停止控制\n");
			currentPhase = PHASE_MISSION_COMPLETE;
			anglevel[0] = tarangle;
			anglevel[1] = 0; // 停止
		} else {
			// 检测障碍物并进行避障
			if (DetectObstacles(pos, p_end)) {
				tarangle = CalculateAvoidanceAngle(pos, p_end, tarangle);
			}
			anglevel[0] = tarangle;
			anglevel[1] = tracking_task.mission[dotTrackingParam.TrackNum].param3;
		}
	} else {
		// 第一段航行不进行避障
		anglevel[0] = tarangle;
		anglevel[1] = tracking_task.mission[dotTrackingParam.TrackNum].param3;
	}

	// 7. 航点及阶段切换的主要逻辑
	if (distance_to_target <= current_switch_distance) {
		dotTrackingParam.TrackNum++; // 切换到下一个目标点
		// 后续的阶段转换将在下面的后处理逻辑中进行
	}

	// 8. 任务阶段转换的后处理逻辑 (当TrackNum增加时触发)
	if (dotTrackingParam.TrackNum > phaseTransitionFlag) {
		if (phaseTransitionFlag == 0 && currentPhase == PHASE_HEADING_CONTROL) {
			// 完成航点0 -> 准备航点1的跟踪 (阶段二)
			currentPhase = PHASE_TRACK_ACCURACY;
			float startPoint[2] = { tracking_task.mission[0].lon, tracking_task.mission[0].lat };
			float endPoint[2] = { tracking_task.mission[1].lon, tracking_task.mission[1].lat };
			InitTrackAccuracyEvaluation(startPoint, endPoint);
			printf("阶段转换 -> 航迹精度评估 (航点1 -> 航点2)\n");
		}
		else if (phaseTransitionFlag == 1 && currentPhase == PHASE_TRACK_ACCURACY) {
			// 完成航点1 -> 准备航点2的跟踪 (阶段三)
			if (trackAccuracyEval.evalStarted) {
				CalculateTrackAccuracy();
				trackAccuracyEval.evalStarted = false;
			}
			currentPhase = PHASE_CURVE_TRACKING;
			printf("阶段转换 -> 曲线跟踪 (航点2 -> ...)\n");
		}

		// 检查任务是否在这次切换后完成
		if (dotTrackingParam.TrackNum >= tracking_task.mission_num) {
			currentPhase = PHASE_MISSION_COMPLETE;
			printf("所有航点完成，任务结束。\n");
		}

		phaseTransitionFlag = dotTrackingParam.TrackNum; // 更新标志，防止重复进入
	}

	// 9. 调用相应的评估函数
	if (currentPhase == PHASE_HEADING_CONTROL) {
		EvaluateHeadingAccuracy(boatstate.PostoHome[3], anglevel[0]);
	} else if (currentPhase == PHASE_TRACK_ACCURACY) {
		EvaluateTrackAccuracy(pos);
	}

	// 10. 输出最终控制指令
	actionModelData.model = 10;
	actionHeadvelControl.model = 2;
	actionHeadvelControl.heading = anglevel[0];
	actionHeadvelControl.velocity = anglevel[1];
}

// ================== 避障功能实现 ==================

void MotionPlan::SetLocalMap(std::vector<std::vector<int>>* mapPtr)
{
	localMapPtr = mapPtr;
}

void MotionPlan::UpdateNearbyShips(const std::vector<kinematic_state>& ships)
{
	nearbyShips = ships;
	obstacleAvoidance.nearbyShips = ships;
}

bool MotionPlan::DetectObstacles(float currentPos[2], float targetPos[2])
{
	obstacleAvoidance.obstacleDetected = false;
	obstacleAvoidance.shipDetected = false;
	obstacleAvoidance.minObstacleDistance = 1000.0f;
	obstacleAvoidance.minShipDistance = 1000.0f;

	// 1. 检测地图障碍物
	if (localMapPtr != nullptr) {
		// 计算从当前位置到目标位置的路径上的障碍物
		float dx = targetPos[0] - currentPos[0];
		float dy = targetPos[1] - currentPos[1];
		float distance = sqrtf(dx*dx + dy*dy);

		if (distance > 1.0f) {
			int steps = (int)(distance / 5.0f); // 每5米检查一次
			if (steps < 10) steps = 10;

			for (int i = 0; i <= steps; i++) {
				float ratio = (float)i / steps;
				float checkX = currentPos[0] + dx * ratio;
				float checkY = currentPos[1] + dy * ratio;

				// 转换为地图坐标 (800x800地图，以船只位置为中心)
				int mapX = (int)(checkX - currentPos[0] + 400);
				int mapY = (int)(checkY - currentPos[1] + 400);

				if (mapX >= 0 && mapX < 800 && mapY >= 0 && mapY < 800) {
					if ((*localMapPtr)[mapX][mapY] == 1) {
						// 发现障碍物，计算距离
						float obstDist = sqrtf((checkX - currentPos[0])*(checkX - currentPos[0]) +
											  (checkY - currentPos[1])*(checkY - currentPos[1]));
						if (obstDist < obstacleAvoidance.minObstacleDistance) {
							obstacleAvoidance.minObstacleDistance = obstDist;
							obstacleAvoidance.obstacleDetected = true;
						}
					}
				}
			}
		}
	}

	// 2. 检测其他船只
	for (const auto& ship : nearbyShips) {
		float shipDist = sqrtf((ship.lat - currentPos[0])*(ship.lat - currentPos[0]) +
							  (ship.lon - currentPos[1])*(ship.lon - currentPos[1]));
		if (shipDist < obstacleAvoidance.minShipDistance) {
			obstacleAvoidance.minShipDistance = shipDist;
			obstacleAvoidance.shipDetected = true;
		}
	}

	return obstacleAvoidance.obstacleDetected || obstacleAvoidance.shipDetected;
}

float MotionPlan::CalculateAvoidanceAngle(float currentPos[2], float targetPos[2], float originalAngle)
{
	if (!obstacleAvoidance.obstacleDetected && !obstacleAvoidance.shipDetected) {
		return originalAngle; // 无需避障
	}

	// 简单的避障策略：向右偏转30度
	float avoidanceOffset = 30.0f;

	// 如果检测到障碍物距离很近，增加偏转角度
	if (obstacleAvoidance.obstacleDetected && obstacleAvoidance.minObstacleDistance < 50.0f) {
		avoidanceOffset = 45.0f;
	}
	if (obstacleAvoidance.shipDetected && obstacleAvoidance.minShipDistance < 60.0f) {
		avoidanceOffset = 45.0f;
	}

	float avoidanceAngle = originalAngle + avoidanceOffset;

	// 角度归一化
	if (avoidanceAngle >= 360.0f) {
		avoidanceAngle -= 360.0f;
	}

	obstacleAvoidance.avoidanceAngle = avoidanceOffset;

	printf("避障：原角度=%.1f°, 避障角度=%.1f°, 偏移=%.1f°\n",
		   originalAngle, avoidanceAngle, avoidanceOffset);

	return avoidanceAngle;
}

bool MotionPlan::CheckMissionFailure(float currentPos[2])
{
	// 检查与障碍物的距离
	if (obstacleAvoidance.obstacleDetected && obstacleAvoidance.minObstacleDistance < 40.0f) {
		printf("任务失败：与障碍物距离 %.1f 米 < 40米限制\n", obstacleAvoidance.minObstacleDistance);
		obstacleAvoidance.missionFailed = true;
		return true;
	}

	// 检查与其他船只的距离
	if (obstacleAvoidance.shipDetected && obstacleAvoidance.minShipDistance < 45.0f) {
		printf("任务失败：与其他船只距离 %.1f 米 < 45米限制\n", obstacleAvoidance.minShipDistance);
		obstacleAvoidance.missionFailed = true;
		return true;
	}

	return false;
}

// ================== 其他避障辅助函数 ==================

float MotionPlan::EvaluatePathSafety(float currentPos[2], float candidateAngle, float targetPos[2])
{
	// 简单的路径安全评估：返回一个安全分数
	// 这里可以根据需要实现更复杂的路径安全评估算法
	float safetyScore = 1.0f; // 默认安全分数

	// 检查候选角度方向上是否有障碍物
	float checkDistance = 100.0f; // 检查前方100米
	float checkX = currentPos[0] + checkDistance * cosf(candidateAngle * M_PI / 180.0f);
	float checkY = currentPos[1] + checkDistance * sinf(candidateAngle * M_PI / 180.0f);

	if (localMapPtr != nullptr) {
		int mapX = (int)(checkX - currentPos[0] + 400);
		int mapY = (int)(checkY - currentPos[1] + 400);

		if (mapX >= 0 && mapX < 800 && mapY >= 0 && mapY < 800) {
			if ((*localMapPtr)[mapX][mapY] == 1) {
				safetyScore = 0.1f; // 发现障碍物，降低安全分数
			}
		}
	}

	return safetyScore;
}

float MotionPlan::GetObstacleDistance(float currentPos[2], float targetPos[2])
{
	// 获取到最近障碍物的距离
	float minDistance = 1000.0f;

	if (localMapPtr != nullptr) {
		// 在当前位置周围搜索障碍物
		for (int dx = -50; dx <= 50; dx += 5) {
			for (int dy = -50; dy <= 50; dy += 5) {
				int mapX = (int)(dx + 400);
				int mapY = (int)(dy + 400);

				if (mapX >= 0 && mapX < 800 && mapY >= 0 && mapY < 800) {
					if ((*localMapPtr)[mapX][mapY] == 1) {
						float distance = sqrtf(dx*dx + dy*dy);
						if (distance < minDistance) {
							minDistance = distance;
						}
					}
				}
			}
		}
	}

	return minDistance;
}

float MotionPlan::CalculateDetourAngle(float start[2], float end[2], float direct_angle)
{
	// 计算绕行角度
	float detourAngle = direct_angle;

	// 简单的绕行策略：向左或向右偏转
	float left_angle = direct_angle - 45.0f;
	float right_angle = direct_angle + 45.0f;

	// 角度归一化
	if (left_angle < 0) left_angle += 360.0f;
	if (right_angle >= 360.0f) right_angle -= 360.0f;

	// 评估左右两个方向的安全性
	float left_safety = EvaluatePathSafety(start, left_angle, end);
	float right_safety = EvaluatePathSafety(start, right_angle, end);

	// 选择更安全的方向
	if (left_safety > right_safety) {
		detourAngle = left_angle;
	} else {
		detourAngle = right_angle;
	}

	return detourAngle;
}