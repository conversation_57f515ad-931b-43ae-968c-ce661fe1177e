cmake_minimum_required(VERSION 3.5)
project(xt_user)

if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(OpenCV REQUIRED)
find_package(nlohmann_json REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(Boost COMPONENTS system REQUIRED)

include_directories(${OPENCV_INCLUDE_DIRS})
link_directories(${OPENCV_LIBRARY_DIRS})
add_definitions(${OPENCV_DEFINITIONS})

#========================
# Build Setup
#========================

add_library(planning_hpp SHARED
            ../include/boat_datadef.cpp
	          motion_plan/motion_plan.cpp
            controller/motion_control.cpp
            controller/pid_control.cpp
            )
target_compile_definitions(planning_hpp
  PRIVATE "MINIMAL_PERCEPTION_DLL")
ament_target_dependencies(planning_hpp rclcpp std_msgs cv_bridge sensor_msgs)

add_executable(${PROJECT_NAME}_node main/planning_main.cpp)
target_link_libraries(${PROJECT_NAME}_node planning_hpp ${OpenCV_LIBS} nlohmann_json::nlohmann_json)
ament_target_dependencies(${PROJECT_NAME}_node rclcpp std_msgs)

#add_executable(astar motion_plan/Astar_cpp.cpp)


install(TARGETS
  planning_hpp
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin)

install(TARGETS
  ${PROJECT_NAME}_node
  #astar
  DESTINATION lib/${PROJECT_NAME}
)

ament_export_dependencies(rosidl_default_runtime)

ament_package()

