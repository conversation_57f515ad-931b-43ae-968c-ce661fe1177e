#include "pid_control.h"
#include <chrono>
#include <memory>
#include <errno.h>
#include <fcntl.h> 
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <termios.h>
#include <unistd.h>
#include <iostream>
#include <time.h>
#include <math.h>

PIDControl::PIDControl(float base_p, float base_i, float base_d, float maxsum, float minoutput, float maxoutput)
{
    // 存储基础PID值
	m_base_kp = base_p;
	m_base_ki = base_i;
	m_base_kd = base_d;

    // 初始化当前PID值
    m_current_kp = m_base_kp;
    m_current_ki = m_base_ki;
    m_current_kd = m_base_kd;

	MaxSum = maxsum;
	MinOutput = minoutput;
	MaxOutput = maxoutput;
	err = 0;
	olderr = 0;
	sumerr = 0;

    // 初始化误差阈值（这些值需要根据您的具体应用来调整）
    m_error_thresh_big = 20.0f;   // 例如：当航向误差大于20度时，我们认为是“大误差”
    m_error_thresh_small = 4.0f;  // 例如：当航向误差小于2度时，我们才启用积分项
}

//核心的PID控制函数，集成了自适应逻辑
float PIDControl::PID_Control(float preerr)
{
	float control;
	err = preerr;
    float abs_err = fabsf(err); // 计算误差的绝对值，用于判断

    // ======================================================
    // --- 1. 自适应参数调整 (Adaptive Gain Scheduling) ---
    // ======================================================

    // a. 根据误差大小，动态调整 Kp 和 Kd
    if (abs_err > m_error_thresh_big)
    {
        // 大误差时：使用较大的 Kp 和 Kd
        // 这里的2.0和1.5是“放大系数”，您可以根据需要调整
        m_current_kp = m_base_kp * 2.5f;
        m_current_kd = m_base_kd * 1.5f;
		m_current_ki = 0;
		sumerr = 0;
    }
	else if (abs_err < m_error_thresh_small)
	{
		m_current_kp = m_base_kp * 0.6f;
        m_current_kd = m_base_kd * 0.8f;
		// 小误差时：启用 Ki，并累加积分项
        m_current_ki = m_base_ki;
        sumerr += err;
        // 积分限幅
        if(sumerr > MaxSum)  sumerr = MaxSum;
        if(sumerr < -MaxSum) sumerr = -MaxSum;
	}
    else 
    {
        // 误差不大时：使用基础的 Kp 和 Kd
        m_current_kp = m_base_kp;
        m_current_kd = m_base_kd;
		m_current_ki = 0;
		sumerr = 0;
    }

    // ======================================================
    // --- 2. 标准PID计算 ---
    // ======================================================

    // 使用【当前帧动态计算出】的PID参数进行计算
	control = m_current_kp * err + m_current_ki * sumerr + m_current_kd * (err - olderr);
	
    olderr = err;

    // 输出限幅
	if(control > MaxOutput)
		control = MaxOutput;
	if(control < MinOutput)
		control = MinOutput;
        
	return control;
}

// PIDControl::PIDControl(float p, float i, float d, float maxsum, float minoutput, float maxoutput)
// {
// 	kp = p;
// 	ki = i;
// 	kd = d;
// 	MaxSum = maxsum;
// 	MinOutput = minoutput;
// 	MaxOutput = maxoutput;
// 	err = 0;
// 	olderr = 0;
// 	sumerr = 0;
// }

// float PIDControl::PID_Control(float preerr)
// {
// 	float control;
// 	err = preerr;
// 	sumerr += err;
// 	if(sumerr > MaxSum)  sumerr = MaxSum;
// 	if(sumerr < -MaxSum) sumerr = -MaxSum;

// 	control = kp * err + ki*sumerr + kd*(err - olderr);
// 	olderr = err;
// 	if(control > MaxOutput)
// 		control = MaxOutput;
// 	if(control < MinOutput)
// 		control = MinOutput;
// 	return control;
// }

