#ifndef _MOTION_CONTROL_H
#define _MOTION_CONTROL_H

#include <string.h>
#include <termios.h>
#include <vector>
#include "../../include/Typedef.h"
#include "../../include/boat_datadef.h"
#include "pid_control.h"
using namespace std;

class MotionControl
{
public:
	MotionControl();
	void SetController(char pretype);

	char usvtype;

	PIDControl headingcontroller;
	PIDControl velocitycontroller;

	actuator_control USVControl(headvel_control target, TransState boatstate);
	actuator_control USVControl(headvel_control target, TransState boatstate, bool initial_boost_active);
};

#endif
