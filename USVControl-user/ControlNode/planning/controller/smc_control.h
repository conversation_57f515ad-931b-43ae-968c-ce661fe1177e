#ifndef SMC_CONTROL_H
#define SMC_CONTROL_H

// class SMCControl
// {
// public:
//     // 构造函数，用于设置SMC参数
//     SMCControl(float c, float k, float phi, float minOutput, float maxOutput);
//     SMCControl() = default;
//     ~SMCControl() = default;

//     // SMC控制计算函数
//     // 输入: error (误差), error_dot (误差变化率)
//     float SMC_Control(float error, float error_dot);

// private:
//     float _c;       // 滑模面参数 (决定趋近速度)
//     float _k;       // 切换增益 (决定鲁棒性强度)
//     float _phi;     // 边界层厚度 (决定抖振抑制程度)

//     float _minOutput; // 控制输出最小值
//     float _maxOutput; // 控制输出最大值

//     // 饱和函数，用于平滑切换
//     float sat(float value);
// };

class SMCControl
{
public:
    // 构造函数，用于设置SMC参数
    SMCControl(float c, float k, float ki, float phi, float maxSum, float minOutput, float maxOutput);
    SMCControl() = default;
    ~SMCControl() = default;

    // SMC控制计算函数
    // 输入: error (误差), error_dot (误差变化率)
    float SMC_Control(float error, float error_dot, float dt);

private:
    float _c;       // 滑模面参数 (决定趋近速度)
    float _k;       // 切换增益 (决定鲁棒性强度)
    float _ki;      // 积分增益
    float _phi;     // 边界层厚度 (决定抖振抑制程度)

    float _sum_error;     // 误差积分累加器
    float _max_sum_error; // 积分限幅

    float _minOutput; // 控制输出最小值
    float _maxOutput; // 控制输出最大值

    // 饱和函数，用于平滑切换
    float sat(float value);
};

#endif // SMC_CONTROL_H