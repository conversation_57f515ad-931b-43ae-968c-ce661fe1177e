#ifndef _PID_CONTROL_H
#define _PID_CONTROL_H

#include <string.h>
#include <termios.h>

class PIDControl
{
public:
    // 构造函数现在接收“基础”PID参数
	PIDControl(float base_p, float base_i, float base_d, float maxsum, float minoutput, float maxoutput);
	PIDControl(void) {}

	float MaxOutput;
	float MinOutput;
	float MaxSum;
	
	float err;
	float olderr;
	float sumerr;

    // --- 新增的自适应相关成员 ---
    float m_base_kp, m_base_ki, m_base_kd; // 存储基础PID值
    float m_current_kp, m_current_ki, m_current_kd; // 存储当前帧动态计算出的PID值

    // 定义误差阈值，用于划分“大误差”和“小误差”区间
    // 您可以根据实际情况调整这两个值
    float m_error_thresh_big;   // 大误差阈值
    float m_error_thresh_small; // 小误差阈值（用于激活Ki）
    // --- 结束新增 ---

	float PID_Control(float error);
};

// class PIDControl
// {
// public:

// 	PIDControl(float p, float i, float d, float maxsum, float minoutput, float maxoutput);
// 	PIDControl(void)
// 	{
		
// 	}

// 	float MaxOutput;
// 	float MinOutput;
// 	float MaxSum;
	
// 	float err;
// 	float olderr;
// 	float sumerr;
// 	float kp, ki, kd;
// 	float PID_Control(float error);
// };

#endif
