#include "smc_control.h"
#include <cmath>

// SMCControl::SMCControl(float c, float k, float phi, float minOutput, float maxOutput)
// {
//     _c = c;
//     _k = k;
//     _phi = phi;
//     _minOutput = minOutput;
//     _maxOutput = maxOutput;
// }

// // 饱和函数 sat(x)
// float SMCControl::sat(float value)
// {
//     if (fabs(value) > 1.0f)
//     {
//         return (value > 0) ? 1.0f : -1.0f;
//     }
//     return value;
// }

// float SMCControl::SMC_Control(float error, float error_dot)
// {
//     // 1. 计算滑模面 s = c*e + de/dt
//     float s = _c * error + error_dot;

//     // 2. 使用边界层内的饱和函数 sat(s/phi) 替代 sgn(s) 来计算控制输出
//     // 控制律 u = -k * sat(s/phi)
//     float control_output = _k * sat(s / _phi);

//     // 3. 对输出进行限幅
//     if (control_output > _maxOutput)
//     {
//         control_output = _maxOutput;
//     }
//     else if (control_output < _minOutput)
//     {
//         control_output = _minOutput;
//     }
    
//     return control_output;
// }



SMCControl::SMCControl(float c, float k, float ki, float phi, float maxSum, float minOutput, float maxOutput)
{
    _c = c;
    _k = k;
    _ki = ki;
    _phi = phi;

    _minOutput = minOutput;
    _maxOutput = maxOutput;
    _max_sum_error = maxSum;

    _sum_error = 0.0f; // 初始化积分项
}

// 饱和函数 sat(x)
float SMCControl::sat(float value)
{
    if (fabs(value) > 1.0f)
    {
        return (value > 0) ? 1.0f : -1.0f;
    }
    return value;
}

// 增加了 dt 参数的控制函数
float SMCControl::SMC_Control(float error, float error_dot, float dt)
{
    // 1. 累加误差积分项
    if (fabs(_ki) > 1e-6f) 
    {
        _sum_error += error * dt;
        // 进行积分限幅 (Anti-Windup)
        if (_sum_error > _max_sum_error) {
            _sum_error = _max_sum_error;
        } else if (_sum_error < -_max_sum_error) {
            _sum_error = -_max_sum_error;
        }
    }

    // 2. 计算新的、带积分项的滑模面 s = c*e + de/dt + ki*∫e dt
    float s = _c * error + error_dot + _ki * _sum_error;

    // 3. 计算控制输出 u = k * sat(s/phi)
    float control_output = _k * sat(s / _phi);

    // 4. 对输出进行限幅
    if (control_output > _maxOutput) {
        control_output = _maxOutput;
    } else if (control_output < _minOutput) {
        control_output = _minOutput;
    }
    
    return control_output;
}