
#ifndef _BOAT_DATADEF_H_
#define _BOAT_DATADEF_H_

#include <stdio.h>
#include <stdlib.h>
#include <ctime>
#include <sys/time.h>
#include "Typedef.h"

//command struct
//the mission item
typedef struct 
{
	uint16_t  command;  
	uint8_t   seq;    
	float  param1;    /*speed, m/s*/
	float  param2;    /*wait time, s*/
	float  param3;
	float  param4;
	float  lat;    /*latitude, deg*/
	float  lon;    /*longitude, deg*/
	float  alt;    /*altitude, m*/
	uint8_t  reserve[6];
} MissionItem;

//the dot position of mission item
typedef struct 
{
	double  lat;   /*latitude, deg*/
	double  lon;   /*longitude, deg*/
} DotPos;

//action flash and model
typedef struct
{
	uint8_t  flash_flag;   /*station command flash flag*/
	uint8_t  model;   /*station command model*/
} flash_model_data;

//direct control, model:0
typedef struct {
	float  steer_l;   /*angle of left steer, deg*/
	float  steer_r;   /*angle of right steer, deg*/
	float  motor_l;   /*rotate speed of left motor, rpm*/
	float  motor_r;   /*rotate speed of right motor, rpm*/
	float  bucket_l;  /*angle of left bucket, deg*/
	float  bucket_r;  /*angle of right bucket, deg*/
	uint8_t  reserve[6];
} actuator_control;

//heading velocity congtrol, model:10
typedef struct  {
	uint8_t  model;  /*0,heading rotate control; 1,heading velocity control*/
	float  heading;  /*heading, deg*/
	float  velocity;    /*velocity, model:0,0~1000; model:1,m/s*/
	uint8_t  reserve[6];
} headvel_control;


//anchor mission control, model:20
typedef struct {
	uint8_t   mission_num;  /*mission numbers*/
	MissionItem  mission[512];    /*missions*/
	uint8_t  reserve[6];
} anchor_mission_control;

//trajectory tracking control, model:21
typedef struct {
	uint8_t   dot_num;  /*trajectory dot num*/
	uint8_t   model;    /*trajectory tracking model*/
	DotPos    dot[128];    /*all dots*/
	uint8_t   reserve[6];
} trajectory_mission_control;


//state data struct

typedef struct{
	uint8_t kind;
	uint8_t num;
	double  lat;   /*latitude, deg*/
	double  lon;   /*longitude, deg*/
	double  alt;   /*heading, deg*/
	float  roll;   /*roll angle, deg*/
	float  pitch;   /*pitch angle, deg*/
	float  yaw;   /*yaw angle, deg*/
	float  rollspeed;   /*deg/s*/
	float  pitchspeed;   /*deg/s*/
	float  yawspeed;   /*deg/s*/
	float vx; /*< [m/s] Ground X Speed (Latitude)*/
	float vy; /*< [m/s] Ground Y Speed (Longitude)*/
	float vz; /*< [m/s] Ground Z Speed (Altitude)*/
	float xacc; /*< [mG] X acceleration*/
 	float yacc; /*< [mG] Y acceleration*/
	float zacc; /*< [mG] Z acceleration*/
	uint8_t  reserve[6];
} kinematic_state;




typedef struct
{
	double HomePos[2];
	double PostoHome[4];  //X,Y,Z,Phi
	double PosEarth[4];   //lat,lon,Z,Phi
	float VelEarth[4];   //VX,VY,VZ,Phi
	float VelPolar[4];   //L,Psi,VZ,Phi
	float VelBody[4];    //u,v,r,Phi
}TransState;

void Dot2Line(float dot[2], float line[2][2], float tardot[2], float unit[2]);


#endif 
