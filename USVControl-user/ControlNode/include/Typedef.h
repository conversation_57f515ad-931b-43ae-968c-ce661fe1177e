
#ifndef _TYPEDEF_H_
    #define _TYPEDEF_H_
    
#ifndef CHAR
    #define CHAR  char
#endif

#ifndef FLOAT
    #define FLOAT  float
#endif

#ifndef DOUBLE
    #define DOUBLE double
#endif

#ifndef LONG
    #define LONG long int
#endif

#ifndef BOOL
    #define BOOL  INT8
#endif

#ifndef TRUE
    #define TRUE  (1)
#endif

#ifndef FALSE
    #define FALSE (0)
#endif

#ifndef NULL
    #define NULL  (0)
#endif

//#ifndef BYTE
//    #define BYTE    CHAR
//#endif

#ifndef MC_COLOR
	#define MC_COLOR   UINT32
#endif

#ifndef uint16_t
    #define uint16_t    u_int16_t
#endif

#ifndef uint8_t
    #define uint8_t    u_int8_t
#endif


#endif // _TYPEDEF_H_
