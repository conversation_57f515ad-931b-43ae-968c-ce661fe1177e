# 船队航行任务配置
version: v1.0.0

# 船只配置
types:
  # 类型0船只 (3艘)
  - type: 2
    ship_count: 1
    ships:
      # 船舶00-19完整航路数据
      - id: 0
        waypoint_count: 13
        waypoints:

          - {command: 16, seq: 1, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 12000.0, y: 2553.0, z: 0.0}
          - {command: 16, seq: 2, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 10760.0, y: 3495.0, z: 0.0}
          - {command: 16, seq: 3, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 9160.0, y: 3805.0, z: 0.0}
          - {command: 16, seq: 4, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 7767.0, y: 2979.0, z: 0.0}
          - {command: 16, seq: 5, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 7276.0, y: 1869.0, z: 0.0}
          - {command: 16, seq: 6, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 6734.0, y: 966.0, z: 0.0}
          - {command: 16, seq: 7, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 5960.0, y: 453.0, z: 0.0}
          - {command: 16, seq: 8, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 5238.0, y: 863.0, z: 0.0}
          - {command: 16, seq: 9, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 4102.0, y: 915.0, z: 0.0}
          - {command: 16, seq: 10, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 3455.0, y: 1021.0, z: 0.0}
          - {command: 16, seq: 11, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 3096.0, y: 1511.0, z: 0.0}
          - {command: 16, seq: 12, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 1651.0, y: 2050.0, z: 0.0}
          - {command: 16, seq: 13, param1: 0.0, param2: 0.0, param3: 8, param4: 0.0, x: 500.0, y: 2105.0, z: 0.0}

          - {command: 16, seq: 1, param1: 0.0, param2: 0.0, param3: 20.0, param4: 0.0, x: 11500.0, y: 2953.0, z: 0.0}
          - {command: 16, seq: 2, param1: 0.0, param2: 0.0, param3: 20.0, param4: 0.0, x: 10530.0, y: 4000.0, z: 0.0}
          - {command: 16, seq: 3, param1: 0.0, param2: 0.0, param3: 20.0, param4: 0.0, x: 9560.0, y: 3695.0, z: 0.0}
          - {command: 16, seq: 4, param1: 0.0, param2: 0.0, param3: 20.0, param4: 0.0, x: 8147.0, y: 2953.0, z: 0.0}
          - {command: 16, seq: 5, param1: 0.0, param2: 0.0, param3: 20.0, param4: 0.0, x: 6734.0, y: 966.0, z: 0.0}
          - {command: 16, seq: 6, param1: 0.0, param2: 0.0, param3: 20.0, param4: 0.0, x: 4102.0, y: 915.0, z: 0.0}
          - {command: 16, seq: 7, param1: 0.0, param2: 0.0, param3: 20.0, param4: 0.0, x: 500.0, y: 2105.0, z: 0.0}


          # --- Sin曲线航迹 (航点2 -> 航点3) ---
          # 起点 (您提供的航点2)
          # - {command: 16, seq: 1, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 11500.0, y: 2953.0, z: 0.0}
          # - {command: 16, seq: 2, param1: 0.0, param2: 0.0, param3: 9.9, param4: 0.0, x: 9560.0, y: 3695.0, z: 0.0}
          
          # # 点 ① (在航线2->3的25%处，右侧偏移800单位)
          # - {command: 16, seq: 1, param1: 0.0, param2: 0.0, param3: 9.0, param4: 0.0, x: 9409.2, y: 2437.3, z: 0.0}
          
          # # 点 ② (在航线2->3的75%处，右侧偏移800单位)
          # - {command: 16, seq: 2, param1: 0.0, param2: 0.0, param3: 9.0, param4: 0.0, x: 7996.2, y: 1273.8, z: 0.0}

          # # 点 ③ (在航线2->3的25%处，左侧偏移800单位)
          # - {command: 16, seq: 3, param1: 0.0, param2: 0.0, param3: 9.0, param4: 0.0, x: 8297.8, y: 3588.2, z: 0.0}
          
          # # 点 ④ (在航线2->3的75%处，左侧偏移800单位)
          # - {command: 16, seq: 4, param1: 0.0, param2: 0.0, param3: 9.0, param4: 0.0, x: 6884.8, y: 2422.7, z: 0.0}
          
          # # 终点 (您提供的航点3, 序号已更新)
          # - {command: 16, seq: 13, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 6734.0, y: 966.0, z: 0.0}


          # # 第一点
          # - {command: 16, seq: 1, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 11500.0, y: 2953.0, z: 0.0}
          # # 第二点
          # - {command: 16, seq: 2, param1: 0.0, param2: 0.0, param3: 9.9, param4: 0.0, x: 9560.0, y: 3695.0, z: 0.0}
          # # 新增点1 (从第二点开始的避障路径)
          # - {command: 16, seq: 3, param1: 0.0, param2: 0.0, param3: 8.0, param4: 0.0, x: 8500.0, y: 2800.0, z: 0.0}
          # # 新增点2 (中间过渡点)
          # - {command: 16, seq: 4, param1: 0.0, param2: 0.0, param3: 8.0, param4: 0.0, x: 7800.0, y: 1900.0, z: 0.0}
          # # 新增点3 (接近原第三点)
          # - {command: 16, seq: 5, param1: 0.0, param2: 0.0, param3: 8.0, param4: 0.0, x: 7200.0, y: 1200.0, z: 0.0}
          # # 原第三点 (现在是第6个点)
          # - {command: 16, seq: 6, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 6734.0, y: 966.0, z: 0.0}
          # # 第四点
          # - {command: 16, seq: 7, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 4102.0, y: 915.0, z: 0.0}
          # # 终点
          # - {command: 16, seq: 8, param1: 0.0, param2: 0.0, param3: 9.9, param4: 0.0, x: 500.0, y: 2105.0, z: 0.0}
 
          # - {command: 16, seq: 1, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 12000.0, y: 2553.0, z: 0.0}
          # - {command: 16, seq: 2, param1: 0.0, param2: 0.0, param3: 9.9, param4: 0.0, x: 10760.0, y: 3495.0, z: 0.0}
          # - {command: 16, seq: 3, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 9160.0, y: 3805.0, z: 0.0}
          # - {command: 16, seq: 4, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 7767.0, y: 2979.0, z: 0.0}
          # - {command: 16, seq: 5, param1: 0.0, param2: 0.0, param3: 9.9, param4: 0.0, x: 7276.0, y: 1869.0, z: 0.0}
          # - {command: 16, seq: 6, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 6734.0, y: 966.0, z: 0.0}
          # - {command: 16, seq: 7, param1: 0.0, param2: 0.0, param3: 9.9, param4: 0.0, x: 5960.0, y: 453.0, z: 0.0}
          # - {command: 16, seq: 8, param1: 0.0, param2: 0.0, param3: 7.3, param4: 0.0, x: 5238.0, y: 863.0, z: 0.0}
          # - {command: 16, seq: 9, param1: 0.0, param2: 0.0, param3: 8.1, param4: 0.0, x: 4102.0, y: 915.0, z: 0.0}
          # - {command: 16, seq: 10, param1: 0.0, param2: 0.0, param3: 8.8, param4: 0.0, x: 3455.0, y: 1021.0, z: 0.0}
          # - {command: 16, seq: 11, param1: 0.0, param2: 0.0, param3: 8.5, param4: 0.0, x: 3096.0, y: 1511.0, z: 0.0}
          # - {command: 16, seq: 12, param1: 0.0, param2: 0.0, param3: 9.0, param4: 0.0, x: 1651.0, y: 2050.0, z: 0.0}
          # - {command: 16, seq: 13, param1: 0.0, param2: 0.0, param3: 8.2, param4: 0.0, x: 500.0, y: 2105.0, z: 0.0}

          # - {command: 16, seq: 1, param1: 0.0, param2: 0.0, param3: 8.2, param4: 0.0, x: 500.0, y: 2105.0, z: 0.0}
          # - {command: 16, seq: 2, param1: 0.0, param2: 0.0, param3: 9.0, param4: 0.0, x: 1651.0, y: 2050.0, z: 0.0}
          # - {command: 16, seq: 3, param1: 0.0, param2: 0.0, param3: 8.5, param4: 0.0, x: 3096.0, y: 1511.0, z: 0.0}
          # - {command: 16, seq: 4, param1: 0.0, param2: 0.0, param3: 8.8, param4: 0.0, x: 3455.0, y: 1021.0, z: 0.0}
          # - {command: 16, seq: 5, param1: 0.0, param2: 0.0, param3: 8.1, param4: 0.0, x: 4102.0, y: 915.0, z: 0.0}
          # - {command: 16, seq: 6, param1: 0.0, param2: 0.0, param3: 7.3, param4: 0.0, x: 5238.0, y: 863.0, z: 0.0}
          # - {command: 16, seq: 7, param1: 0.0, param2: 0.0, param3: 9.9, param4: 0.0, x: 5960.0, y: 453.0, z: 0.0}
          # - {command: 16, seq: 8, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 6734.0, y: 966.0, z: 0.0}
          # - {command: 16, seq: 9, param1: 0.0, param2: 0.0, param3: 9.9, param4: 0.0, x: 7276.0, y: 1869.0, z: 0.0}
          # - {command: 16, seq: 10, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 7767.0, y: 2979.0, z: 0.0}
          # - {command: 16, seq: 11, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 9160.0, y: 3805.0, z: 0.0}
          # - {command: 16, seq: 12, param1: 0.0, param2: 0.0, param3: 9.9, param4: 0.0, x: 10760.0, y: 3495.0, z: 0.0}
          # - {command: 16, seq: 13, param1: 0.0, param2: 0.0, param3: 8.4, param4: 0.0, x: 12670.0, y: 2153.0, z: 0.0}

