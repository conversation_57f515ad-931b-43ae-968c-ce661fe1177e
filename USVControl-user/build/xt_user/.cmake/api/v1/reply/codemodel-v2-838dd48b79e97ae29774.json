{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-3efce3d31c25775e1bd5.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "xt_user", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "planning_hpp::@6890427a1f51a3e7e1df", "jsonFile": "target-planning_hpp-c178d62df75ed7337dfc.json", "name": "planning_hpp", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-f4d13bfd00e9de3f2234.json", "name": "uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "xt_user_node::@6890427a1f51a3e7e1df", "jsonFile": "target-xt_user_node-fee42b58a51370192200.json", "name": "xt_user_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "xt_user_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-xt_user_uninstall-59f54cb2afda67685e4a.json", "name": "xt_user_uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/project2_new/USVControl-user/build/xt_user", "source": "/home/<USER>/project2_new/USVControl-user/ControlNode/planning"}, "version": {"major": 2, "minor": 3}}