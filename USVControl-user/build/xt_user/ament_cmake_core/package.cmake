set(_AMENT_PACKAGE_NAME "xt_user")
set(xt_user_VERSION "0.7.4")
set(xt_user_MAINTAINER "Bin Liu <<EMAIL>>")
set(xt_user_BUILD_DEPENDS "rclcpp" "boat_msgs" "rttest" "tlsf_cpp" "nlohmann_json")
set(xt_user_BUILDTOOL_DEPENDS "ament_cmake")
set(xt_user_BUILD_EXPORT_DEPENDS "nlohmann_json")
set(xt_user_BUILDTOOL_EXPORT_DEPENDS )
set(xt_user_EXEC_DEPENDS "rclcpp" "boat_msgs" "rttest" "tlsf_cpp" "nlohmann_json")
set(xt_user_TEST_DEPENDS "ament_cmake_pytest" "ament_lint_auto" "ament_lint_common" "launch" "launch_testing" "launch_testing_ament_cmake" "launch_testing_ros" "rmw_implementation_cmake" "ros2run")
set(xt_user_GROUP_DEPENDS )
set(xt_user_MEMBER_OF_GROUPS )
set(xt_user_DEPRECATED "")
set(xt_user_EXPORT_TAGS)
list(APPEND xt_user_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
