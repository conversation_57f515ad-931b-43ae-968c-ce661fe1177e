#!/usr/bin/env python3
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
import os
from pathlib import Path

def generate_launch_description():
    # 获取当前launch文件所在目录
    current_dir = Path(__file__).parent.resolve()
    
    # 构建其他launch文件的完整路径
    usv_launch_path = str(current_dir / "node.launch_usv.py")
    plan_launch_path = str(current_dir / "node.launch_plan.py")
    env_launch_path = str(current_dir / "node.launch_env.py")
    
    # 验证路径是否存在
    if not os.path.exists(usv_launch_path):
        raise FileNotFoundError(f"找不到文件: {usv_launch_path}")
    
    return LaunchDescription([
        # 包含传感器融合启动文件
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource([usv_launch_path])
        ),
        
        # 包含导航启动文件
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource([plan_launch_path])
        ),
        
        # 包含USV控制启动文件
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource([env_launch_path])
        )
    ])